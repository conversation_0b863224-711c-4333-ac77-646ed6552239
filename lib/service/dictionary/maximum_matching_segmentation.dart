import 'dart:async';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// A more accurate Chinese segmentation service using Maximum Matching algorithm
class MaximumMatchingSegmentationService {
  static final MaximumMatchingSegmentationService _instance =
      MaximumMatchingSegmentationService._internal();

  /// Singleton instance
  factory MaximumMatchingSegmentationService() => _instance;

  MaximumMatchingSegmentationService._internal();

  /// Flag to track initialization status
  bool _isInitialized = false;

  /// Lock for synchronization
  final _lock = Object();

  /// The word dictionary - contains Chinese words and their frequencies
  final Map<String, int> _wordDict = {};

  /// Maximum word length in the dictionary
  int _maxWordLength = 0;

  /// Minimum word frequency to consider
  final int _minFrequency = 5;

  /// Chinese punctuation marks that should be treated as separate tokens
  final Set<String> _punctuation = {
    '，',
    '。',
    '！',
    '？',
    '：',
    '；',
    '"',
    '"',
    ''', ''',
    '（',
    '）',
    '【',
    '】',
    '《',
    '》',
    '、',
    '…',
    '—',
    '～',
    '·',
    '〈',
    '〉',
    '「',
    '」',
  };

  /// Completer for initialization to handle concurrent calls
  static Completer<void>? _initCompleter;

  /// Initialize the segmentation service by loading the dictionary
  Future<void> initialize() async {
    // Quick check without lock
    if (_isInitialized) return;

    // Use completer-based synchronization to handle concurrent calls
    if (_initCompleter != null) {
      // Another initialization is in progress, wait for it
      return await _initCompleter!.future;
    }

    // Create completer and start initialization
    _initCompleter = Completer<void>();

    try {
      AnxLog.info('Initializing Maximum Matching Chinese segmentation service');

      // Double-check after acquiring the completer
      if (_isInitialized) {
        _initCompleter!.complete();
        return;
      }

      // Load built-in dictionary or embedded dictionary
      await _loadBuiltInDictionary();

      // Load custom dictionary if exists in assets
      try {
        await _loadDictionaryFromAssets();
      } catch (e) {
        AnxLog.info('No custom dictionary found, using built-in dictionary');
      }

      _isInitialized = true;
      AnxLog.info(
        'Chinese segmentation service initialized with ${_wordDict.length} words',
      );
      _initCompleter!.complete();
    } catch (e) {
      AnxLog.severe('Failed to initialize Chinese segmentation service: $e');
      _initCompleter!.complete();
    } finally {
      _initCompleter = null;
    }
  }

  /// Load a built-in dictionary with most common Chinese words
  Future<void> _loadBuiltInDictionary() async {
    // Most common Chinese words with frequency
    // Format: word frequency
    final commonWords = [
      '的 10000',
      '一 9000',
      '是 8500',
      '不 8000',
      '了 7500',
      '在 7000',
      '人 6500',
      '有 6000',
      '我 5500',
      '他 5000',
      '这 4800',
      '个 4700',
      '们 4600',
      '中 4500',
      '来 4400',
      '上 4300',
      '大 4200',
      '为 4100',
      '和 4000',
      '国 3900',
      '地 3800',
      '到 3700',
      '以 3600',
      '说 3500',
      '时 3400',
      '要 3300',
      '就 3200',
      '出 3100',
      '会 3000',
      '可 2900',
      '也 2800',
      '你 2700',
      '对 2600',
      '生 2500',
      '能 2400',
      '而 2300',
      '子 2200',
      '那 2100',
      '得 2000',
      '于 1900',
      '着 1800',
      '下 1700',
      '自 1600',
      '之 1500',
      '年 1400',
      '过 1300',
      '发 1200',
      '后 1100',
      '作 1000',
      '里 900',
      '用 800',
      '道 700',
      '行 600',
      '所 500',
      '然 400',
      '家 300',
      '种 200',
      '事 100',
      '成 90',
      '方 80',
      '多 70',
      '经 60',
      '么 50',
      '去 40',
      '法 30',
      '学 20',
      '如 10',
      // Common two-character words
      '中国 9000',
      '人民 8500',
      '学生 8000',
      '工作 7500',
      '时间 7000',
      '问题 6500',
      '生活 6000',
      '国家 5500',
      '因为 5000',
      '所以 4800',
      '如果 4700',
      '什么 4600',
      '怎么 4500',
      '知道 4400',
      '可以 4300',
      '这个 4200',
      '那个 4100',
      '很多 4000',
      '认为 3900',
      '觉得 3800',
      '喜欢 3700',
      '东西 3600',
      '今天 3500',
      '明天 3400',
      '昨天 3300',
      '现在 3200',
      '已经 3100',
      '哪里 3000',
      '为什么 2900',
      '怎么样 2800',
      '不知道 2700',
      '没关系 2600',
      '对不起 2500',
      '谢谢你 2400',
      '不客气 2300',
      '没问题 2200',
      '你好吗 2100',
      '再见了 2000',
      '一起来 1900',
      '请问你 1800',
      '我认为 1700',
    ];

    for (final line in commonWords) {
      final parts = line.trim().split(' ');
      if (parts.length >= 2) {
        final word = parts[0];
        final frequency = int.tryParse(parts[1]) ?? 0;

        if (frequency >= _minFrequency) {
          _wordDict[word] = frequency;
          if (word.length > _maxWordLength) {
            _maxWordLength = word.length;
          }
        }
      }
    }
  }

  /// Load dictionary from assets folder
  Future<void> _loadDictionaryFromAssets() async {
    try {
      final String dict =
          await rootBundle.loadString('assets/dict/chinese_dict.txt');
      final List<String> lines = const LineSplitter().convert(dict);

      for (final line in lines) {
        final parts = line.trim().split(' ');
        if (parts.length >= 2) {
          final word = parts[0];
          final frequency = int.tryParse(parts[1]) ?? 0;

          if (frequency >= _minFrequency) {
            _wordDict[word] = frequency;
            if (word.length > _maxWordLength) {
              _maxWordLength = word.length;
            }
          }
        }
      }
    } catch (e) {
      AnxLog.warning('Error loading dictionary from assets: $e');
      rethrow;
    }
  }

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Forward Maximum Matching algorithm for Chinese segmentation
  List<String> _forwardMaximumMatching(String text) {
    List<String> result = [];
    int i = 0;

    while (i < text.length) {
      // Check for non-Chinese characters (handle as a separate token)
      if (!_isChinese(text[i]) || _punctuation.contains(text[i])) {
        // Group consecutive non-Chinese characters
        int j = i;
        while (j + 1 < text.length &&
            !_isChinese(text[j + 1]) &&
            !_punctuation.contains(text[j + 1]) &&
            _isSameType(text[j], text[j + 1])) {
          j++;
        }
        result.add(text.substring(i, j + 1));
        i = j + 1;
        continue;
      }

      // For Chinese characters, find the longest matching word
      int maxMatchLength = 0;
      for (int j = _maxWordLength; j >= 1; j--) {
        if (i + j <= text.length) {
          String word = text.substring(i, i + j);
          if (_wordDict.containsKey(word)) {
            maxMatchLength = j;
            break;
          }
        }
      }

      // If no match found, treat the character as a separate token
      if (maxMatchLength == 0) {
        result.add(text[i]);
        i++;
      } else {
        result.add(text.substring(i, i + maxMatchLength));
        i += maxMatchLength;
      }
    }

    return result;
  }

  /// Backward Maximum Matching algorithm for Chinese segmentation
  List<String> _backwardMaximumMatching(String text) {
    List<String> result = [];
    int i = text.length;

    while (i > 0) {
      // Check for non-Chinese characters (handle as a separate token)
      if (!_isChinese(text[i - 1]) || _punctuation.contains(text[i - 1])) {
        // Group consecutive non-Chinese characters
        int j = i - 1;
        while (j - 1 >= 0 &&
            !_isChinese(text[j - 1]) &&
            !_punctuation.contains(text[j - 1]) &&
            _isSameType(text[j], text[j - 1])) {
          j--;
        }
        result.insert(0, text.substring(j, i));
        i = j;
        continue;
      }

      // For Chinese characters, find the longest matching word
      int maxMatchLength = 0;
      for (int j = _maxWordLength; j >= 1; j--) {
        if (i - j >= 0) {
          String word = text.substring(i - j, i);
          if (_wordDict.containsKey(word)) {
            maxMatchLength = j;
            break;
          }
        }
      }

      // If no match found, treat the character as a separate token
      if (maxMatchLength == 0) {
        result.insert(0, text[i - 1]);
        i--;
      } else {
        result.insert(0, text.substring(i - maxMatchLength, i));
        i -= maxMatchLength;
      }
    }

    return result;
  }

  /// Bidirectional Maximum Matching algorithm (combines forward and backward)
  List<String> _bidirectionalMaximumMatching(String text) {
    // Get segmentation from both directions
    List<String> forwardResult = _forwardMaximumMatching(text);
    List<String> backwardResult = _backwardMaximumMatching(text);

    // Choose the better segmentation based on heuristics
    if (forwardResult.length != backwardResult.length) {
      // Choose the one with fewer segments
      return forwardResult.length < backwardResult.length
          ? forwardResult
          : backwardResult;
    } else {
      // If they have the same number of segments, choose the one with more single characters
      // This is counterintuitive but works well for Chinese - we prefer longer words
      int forwardSingleChars = forwardResult
          .where((word) => word.length == 1 && _isChinese(word))
          .length;
      int backwardSingleChars = backwardResult
          .where((word) => word.length == 1 && _isChinese(word))
          .length;

      // Fewer single characters is better
      return forwardSingleChars < backwardSingleChars
          ? forwardResult
          : backwardResult;
    }
  }

  /// Segment Chinese text into words and return the word boundaries
  Future<List<List<int>>> getWordBoundaries(String text) async {
    if (!_isInitialized) {
      await initialize();
    }

    return synchronized(() async {
      final result = <List<int>>[];
      try {
        // Use bidirectional maximum matching for more accurate segmentation
        List<String> segments = _bidirectionalMaximumMatching(text);

        // Convert segments to boundary positions
        int startPos = 0;
        for (String segment in segments) {
          int endPos = startPos + segment.length;
          result.add([startPos, endPos]);
          startPos = endPos;
        }

        AnxLog.info(
          'Segmented Chinese text into ${result.length} parts using Maximum Matching',
        );
      } catch (e) {
        AnxLog.severe('Error segmenting Chinese text: $e');
        // Fallback: treat each character as a separate word
        for (int i = 0; i < text.length; i++) {
          result.add([i, i + 1]);
        }
      }
      return result;
    });
  }

  /// Test the segmentation with a sample text and return the segmented words
  Future<List<String>> testSegmentation(String text) async {
    if (!_isInitialized) {
      await initialize();
    }

    AnxLog.info('Testing segmentation for: "$text"');

    List<String> segments = _bidirectionalMaximumMatching(text);

    AnxLog.info(
      'Segmented words (${segments.length}): ${segments.join(' | ')}',
    );
    return segments;
  }

  /// Test reverse segmentation (backward maximum matching)
  Future<List<String>> testSegmentationReverse(String text) async {
    if (!_isInitialized) {
      await initialize();
    }

    AnxLog.info('Testing reverse segmentation for: "$text"');

    List<String> segments = _backwardMaximumMatching(text);

    AnxLog.info(
      'Reverse segmented words (${segments.length}): ${segments.join(' | ')}',
    );
    return segments;
  }

  /// Get the dictionary for use in isolated segmentation
  Future<Map<String, bool>> getDictionary() async {
    if (!_isInitialized) {
      await initialize();
    }

    // Convert frequency dictionary to a simple lookup dictionary
    // This is more efficient for transferring to an isolate
    final Map<String, bool> lookupDict = {};
    for (final word in _wordDict.keys) {
      lookupDict[word] = true;
    }

    return lookupDict;
  }

  /// Check if a character is Chinese
  bool _isChinese(String char) {
    // Unicode range for common Chinese characters
    return RegExp(r'[\u4e00-\u9fa5]').hasMatch(char);
  }

  /// Check if a character is a digit
  bool _isDigit(String char) {
    return RegExp(r'[0-9０-９]').hasMatch(char);
  }

  /// Check if a character is a letter
  bool _isLetter(String char) {
    return RegExp(r'[a-zA-Z]').hasMatch(char);
  }

  /// Check if two characters are of the same type
  bool _isSameType(String char1, String char2) {
    return (_isDigit(char1) && _isDigit(char2)) ||
        (_isLetter(char1) && _isLetter(char2)) ||
        (!_isDigit(char1) &&
            !_isLetter(char1) &&
            !_isChinese(char1) &&
            !_isDigit(char2) &&
            !_isLetter(char2) &&
            !_isChinese(char2));
  }

  /// Helper function for synchronized blocks
  Future<T> synchronized<T>(Future<T> Function() fn) async {
    if (!_lock.toString().contains('Mutex')) {
      // If using a simple Object for locking
      return await fn();
    } else {
      // Future implementation with proper mutex if needed
      return await fn();
    }
  }

  /// Dispose resources
  void dispose() {
    _isInitialized = false;
    _wordDict.clear();
  }
}
