import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:just_audio/just_audio.dart';
import 'package:flutter/material.dart';
import 'package:dasso_reader/models/dictionary/dictionary_entry.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

/// Service for handling online dictionary lookups and pronunciation
class OnlineDictionaryService {
  static final OnlineDictionaryService _instance =
      OnlineDictionaryService._internal();
  factory OnlineDictionaryService() => _instance;

  OnlineDictionaryService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();

  // HTTP client
  final http.Client _httpClient = http.Client();

  // Cache for dictionary lookups to reduce API calls
  final Map<String, DictionaryEntry> _dictionaryCache = {};
  final Map<String, CharacterInfo> _characterInfoCache = {};

  // Persistent cache keys
  static const String _dictionaryCacheKey = 'dictionary_cache';
  static const String _characterInfoCacheKey = 'character_info_cache';

  // Flag to track if we're in offline mode
  bool _isOfflineMode = false;

  /// Initialize the service and load cached data
  Future<void> initialize() async {
    await _loadCachedData();
  }

  /// Load cached dictionary and character info from SharedPreferences
  Future<void> _loadCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load dictionary cache
      final dictionaryJson = prefs.getString(_dictionaryCacheKey);
      if (dictionaryJson != null) {
        final Map<String, dynamic> data = json.decode(dictionaryJson);
        data.forEach((key, value) {
          _dictionaryCache[key] = DictionaryEntry.fromMap(value);
        });
        AnxLog.info(
          'Loaded ${_dictionaryCache.length} dictionary entries from cache',
        );
      }

      // Load character info cache
      final characterInfoJson = prefs.getString(_characterInfoCacheKey);
      if (characterInfoJson != null) {
        final Map<String, dynamic> data = json.decode(characterInfoJson);
        data.forEach((key, value) {
          _characterInfoCache[key] = CharacterInfo.fromMap(value);
        });
        AnxLog.info(
          'Loaded ${_characterInfoCache.length} character info entries from cache',
        );
      }
    } catch (e) {
      AnxLog.severe('Error loading cached data: $e');
    }
  }

  /// Save cached data to SharedPreferences
  Future<void> _saveCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save dictionary cache (limit to 1000 entries to avoid storage issues)
      final Map<String, dynamic> dictionaryData = {};
      final entries = _dictionaryCache.entries.take(1000).toList();
      for (final entry in entries) {
        dictionaryData[entry.key] = entry.value.toMap();
      }
      await prefs.setString(_dictionaryCacheKey, json.encode(dictionaryData));

      // Save character info cache
      final Map<String, dynamic> characterInfoData = {};
      for (final entry in _characterInfoCache.entries) {
        characterInfoData[entry.key] = entry.value.toMap();
      }
      await prefs.setString(
        _characterInfoCacheKey,
        json.encode(characterInfoData),
      );

      AnxLog.info(
        'Saved ${dictionaryData.length} dictionary entries and ${characterInfoData.length} character info entries to cache',
      );
    } catch (e) {
      AnxLog.severe('Error saving cached data: $e');
    }
  }

  /// Set offline mode
  void setOfflineMode(bool isOffline) {
    _isOfflineMode = isOffline;
    AnxLog.info('Dictionary service offline mode set to: $_isOfflineMode');
  }

  /// Play pronunciation for a Chinese word or character
  Future<bool> playPronunciation(String text, {BuildContext? context}) async {
    if (_isOfflineMode) {
      AnxLog.info('Skipping pronunciation in offline mode');
      if (context != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Pronunciation not available in offline mode'),
            duration: Duration(seconds: 2),
          ),
        );
      }
      return false;
    }

    try {
      // Try primary TTS service
      final url =
          'https://api.ttsmaker.com/v1/speak?text=$text&voice=zh-CN-female&format=mp3&speed=1.0';

      try {
        await _audioPlayer.setUrl(url);
        await _audioPlayer.play();
        return true;
      } catch (e) {
        AnxLog.info('Primary TTS service failed, trying fallback: $e');
        // Try fallback service (Google Translate TTS)
        final fallbackUrl =
            'https://translate.google.com/translate_tts?ie=UTF-8&q=$text&tl=zh-CN&client=tw-ob';

        try {
          await _audioPlayer.setUrl(fallbackUrl);
          await _audioPlayer.play();
          return true;
        } catch (e) {
          AnxLog.severe('All TTS services failed: $e');
          // Show error message if context is provided and still mounted
          if (context != null && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Could not play pronunciation for "$text"'),
                duration: const Duration(seconds: 2),
              ),
            );
          }
          return false;
        }
      }
    } catch (e) {
      AnxLog.severe('Error playing audio: $e');
      if (context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error playing audio: $e'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
      return false;
    }
  }

  /// Look up a Chinese word or character in online dictionary
  Future<DictionaryEntry?> lookupChinese(String word) async {
    if (word.isEmpty) return null;

    // Check cache first
    if (_dictionaryCache.containsKey(word)) {
      AnxLog.info('Dictionary cache hit for "$word"');
      return _dictionaryCache[word];
    }

    // If in offline mode, return null if not in cache
    if (_isOfflineMode) {
      AnxLog.info('Dictionary lookup skipped in offline mode for "$word"');
      return null;
    }

    AnxLog.info('Looking up Chinese word online: "$word"');

    try {
      // Try MDBG API first (unofficial API, but reliable)
      final response = await _httpClient.get(
        Uri.parse(
          'https://api.mdbg.net/chinese/dictionary?word=$word&fields=traditional,simplified,pinyin,definitions',
        ),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data != null &&
            data.containsKey('results') &&
            data['results'].isNotEmpty) {
          final result = data['results'][0];

          // Prioritize simplified Chinese - use simplified form for both fields
          final simplified = result['simplified'] ?? word;

          final entry = DictionaryEntry(
            traditional: simplified, // Use simplified for both fields
            simplified: simplified,
            pinyin: result['pinyin'] ?? '',
            definitions: _parseDefinitions(result['definitions'] ?? ''),
            hskLevel: result['hsk_level'] as int?,
            frequency: result['frequency'] as int?,
          );

          // Cache the result
          _dictionaryCache[word] = entry;
          // Save cache periodically
          _saveCachedData();
          return entry;
        }
      }

      // Try fallback to Wiktionary API
      final wiktResponse = await _httpClient.get(
        Uri.parse(
          'https://en.wiktionary.org/api/rest_v1/page/definition/$word',
        ),
      );

      if (wiktResponse.statusCode == 200) {
        final data = json.decode(wiktResponse.body);
        if (data != null && data.containsKey('zh') && data['zh'].isNotEmpty) {
          final zhData = data['zh'][0];

          // Extract definitions
          List<String> definitions = [];
          if (zhData.containsKey('definitions') &&
              zhData['definitions'].isNotEmpty) {
            definitions = (zhData['definitions'] as List)
                .map((def) => def['definition'].toString())
                .toList();
          }

          final entry = DictionaryEntry(
            traditional: word, // Use the same word for both fields
            simplified: word,
            pinyin: '', // Wiktionary doesn't provide pinyin in the API
            definitions: definitions,
            hskLevel: 0,
            frequency: 0,
          );

          // Cache the result
          _dictionaryCache[word] = entry;
          // Save cache periodically
          _saveCachedData();
          return entry;
        }
      }

      // Try third fallback to Chinese Tools API
      final ctResponse = await _httpClient.get(
        Uri.parse(
          'https://www.chinese-tools.com/tools/chinese-dictionary-api.html?q=$word',
        ),
      );

      if (ctResponse.statusCode == 200) {
        // Parse HTML response (simplified implementation)
        final html = ctResponse.body;

        // Extract pinyin and definition using regex (simplified)
        final pinyinMatch =
            RegExp(r'<span class="pinyin">(.*?)</span>').firstMatch(html);
        final defMatch =
            RegExp(r'<div class="definition">(.*?)</div>').firstMatch(html);

        if (pinyinMatch != null || defMatch != null) {
          final definitions =
              defMatch?.group(1)?.replaceAll(RegExp(r'<[^>]*>'), '') ?? '';

          final entry = DictionaryEntry(
            traditional: word, // Use the same word for both fields
            simplified: word,
            pinyin: pinyinMatch?.group(1) ?? '',
            definitions: _parseDefinitions(definitions),
            hskLevel: 0,
            frequency: 0,
          );

          // Cache the result
          _dictionaryCache[word] = entry;
          // Save cache periodically
          _saveCachedData();
          return entry;
        }
      }
    } catch (e) {
      AnxLog.severe('Error looking up word online: $e');
    }

    // If all online lookups fail, create a basic entry
    AnxLog.info(
      'No online dictionary entry found for "$word", creating basic entry',
    );
    final basicEntry = DictionaryEntry(
      traditional: word, // Use the same word for both fields
      simplified: word,
      pinyin: '',
      definitions: ['No definition available'],
      hskLevel: 0,
      frequency: 0,
    );

    // Cache the basic entry
    _dictionaryCache[word] = basicEntry;
    return basicEntry;
  }

  /// Parse definition string into a list of definitions
  List<String> _parseDefinitions(String definitionsStr) {
    if (definitionsStr.isEmpty) return ['No definition available'];
    return definitionsStr.split('/').where((def) => def.isNotEmpty).toList();
  }

  /// Get character information
  Future<CharacterInfo?> getCharacterInfo(String character) async {
    if (character.isEmpty) return null;

    // Check cache first
    if (_characterInfoCache.containsKey(character)) {
      AnxLog.info('Character info cache hit for "$character"');
      return _characterInfoCache[character];
    }

    // If in offline mode, return null if not in cache
    if (_isOfflineMode) {
      AnxLog.info(
        'Character info lookup skipped in offline mode for "$character"',
      );
      return null;
    }

    AnxLog.info('Getting character info for: "$character"');

    try {
      // Try to get basic character info
      final response = await _httpClient.get(
        Uri.parse('https://api.ctext.org/getcharacter?char=$character'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data != null && data.containsKey('radical')) {
          // Create character info with basic data
          final characterInfo = CharacterInfo(
            character: character,
            radical: data['radical'] ?? '',
            components: [character],
          );

          // Cache the result
          _characterInfoCache[character] = characterInfo;
          // Save cache periodically
          _saveCachedData();

          return characterInfo;
        }
      }
    } catch (e) {
      AnxLog.severe('Error getting character info: $e');
    }

    // If all lookups fail, create a basic entry
    final basicInfo = CharacterInfo(
      character: character,
      radical: '',
      components: [character],
    );

    // Cache the basic entry
    _characterInfoCache[character] = basicInfo;
    return basicInfo;
  }

  /// Find words containing a specific character
  Future<List<DictionaryEntry>> findWordsWithCharacter(String character) async {
    if (character.isEmpty) return [];

    // If in offline mode, only return cached entries
    if (_isOfflineMode) {
      AnxLog.info('Finding words with character in offline mode: "$character"');
      final cachedEntries = _dictionaryCache.values
          .where(
            (entry) =>
                entry.simplified.contains(character) ||
                entry.traditional.contains(character),
          )
          .toList();

      return cachedEntries;
    }

    AnxLog.info('Finding words with character online: "$character"');

    try {
      // Try MDBG API to find words containing the character
      final response = await _httpClient.get(
        Uri.parse(
          'https://api.mdbg.net/chinese/dictionary?word=$character&fields=words',
        ),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data != null &&
            data.containsKey('words') &&
            data['words'].isNotEmpty) {
          final words = data['words'] as List;
          final entries = <DictionaryEntry>[];

          for (final word in words) {
            if (word.containsKey('simplified') &&
                word['simplified'].toString().contains(character)) {
              final entry = DictionaryEntry(
                traditional: word['traditional'] ?? word['simplified'],
                simplified: word['simplified'],
                pinyin: word['pinyin'] ?? '',
                definitions: _parseDefinitions(word['definitions'] ?? ''),
                hskLevel: word['hsk_level'] as int?,
                frequency: word['frequency'] as int?,
              );
              entries.add(entry);

              // Cache the entry
              _dictionaryCache[word['simplified']] = entry;
            }
          }

          // Save cache periodically
          _saveCachedData();

          return entries;
        }
      }

      // If no results, return an empty list
      return [];
    } catch (e) {
      AnxLog.severe('Error finding words with character: $e');

      // In case of error, return cached entries containing the character
      final cachedEntries = _dictionaryCache.values
          .where(
            (entry) =>
                entry.simplified.contains(character) ||
                entry.traditional.contains(character),
          )
          .toList();

      return cachedEntries;
    }
  }

  /// Clear the cache
  Future<void> clearCache() async {
    _dictionaryCache.clear();
    _characterInfoCache.clear();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_dictionaryCacheKey);
      await prefs.remove(_characterInfoCacheKey);
      AnxLog.info('Dictionary cache cleared');
    } catch (e) {
      AnxLog.severe('Error clearing dictionary cache: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _audioPlayer.dispose();
    _saveCachedData();

    _httpClient.close();
  }
}
