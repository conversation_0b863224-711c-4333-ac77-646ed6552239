import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/state_management/state_flow_debugger.dart';

/// Race condition detection and state consistency system for Dasso Reader
///
/// This system provides:
/// - Automatic detection of concurrent state updates
/// - State integrity validation
/// - Coordinated provider updates with locking
/// - Automatic error recovery and state rollback
/// - Real-time consistency monitoring

/// Race condition detection result
class RaceConditionDetection {
  final String providerId;
  final List<String> conflictingOperations;
  final DateTime detectedAt;
  final String severity; // 'low', 'medium', 'high', 'critical'
  final String description;

  const RaceConditionDetection({
    required this.providerId,
    required this.conflictingOperations,
    required this.detectedAt,
    required this.severity,
    required this.description,
  });
}

/// State consistency validation result
class StateConsistencyResult {
  final String providerId;
  final bool isValid;
  final List<String> violations;
  final DateTime validatedAt;
  final Map<String, dynamic>? suggestedFix;

  const StateConsistencyResult({
    required this.providerId,
    required this.isValid,
    required this.violations,
    required this.validatedAt,
    this.suggestedFix,
  });
}

/// State operation lock
class StateLock {
  final String providerId;
  final String operationId;
  final DateTime acquiredAt;
  final Duration timeout;
  final Completer<void> _completer = Completer<void>();

  StateLock({
    required this.providerId,
    required this.operationId,
    required this.acquiredAt,
    this.timeout = const Duration(seconds: 30),
  });

  Future<void> get completed => _completer.future;
  void release() => _completer.complete();
  bool get isExpired => DateTime.now().difference(acquiredAt) > timeout;
}

/// Race condition detector
class RaceConditionDetector {
  static RaceConditionDetector? _instance;
  static RaceConditionDetector get instance =>
      _instance ??= RaceConditionDetector._();

  RaceConditionDetector._();

  final Map<String, List<DateTime>> _operationTimestamps = {};
  final Map<String, String> _activeOperations = {};
  final Queue<RaceConditionDetection> _detectionHistory = Queue();
  final StreamController<RaceConditionDetection> _detectionStream =
      StreamController.broadcast();

  static const Duration _raceDetectionWindow = Duration(milliseconds: 100);
  static const int _maxConcurrentOperations = 3;

  /// Track a state operation
  void trackOperation(String providerId, String operationId) {
    final now = DateTime.now();

    // Check for existing active operation
    if (_activeOperations.containsKey(providerId)) {
      final existingOp = _activeOperations[providerId]!;
      _detectRaceCondition(providerId, [existingOp, operationId]);
    }

    // Track operation timing
    _operationTimestamps.putIfAbsent(providerId, () => []).add(now);
    _activeOperations[providerId] = operationId;

    // Clean old timestamps
    _cleanOldTimestamps(providerId);

    // Check for rapid successive operations
    _checkRapidOperations(providerId);
  }

  /// Mark operation as completed
  void completeOperation(String providerId, String operationId) {
    if (_activeOperations[providerId] == operationId) {
      _activeOperations.remove(providerId);
    }
  }

  /// Detect race condition
  void _detectRaceCondition(String providerId, List<String> operations) {
    final detection = RaceConditionDetection(
      providerId: providerId,
      conflictingOperations: operations,
      detectedAt: DateTime.now(),
      severity: _calculateSeverity(providerId, operations),
      description: _generateDescription(providerId, operations),
    );

    _detectionHistory.add(detection);
    _detectionStream.add(detection);

    if (kDebugMode) {
      AnxLog.warning(
        '⚠️ Race condition detected in $providerId: ${operations.join(', ')}',
      );
    }
  }

  /// Check for rapid successive operations
  void _checkRapidOperations(String providerId) {
    final timestamps = _operationTimestamps[providerId] ?? [];
    if (timestamps.length < 2) return;

    final recentOps = timestamps
        .where((t) => DateTime.now().difference(t) < _raceDetectionWindow)
        .length;

    if (recentOps > _maxConcurrentOperations) {
      _detectRaceCondition(
        providerId,
        List.generate(recentOps, (i) => 'rapid_op_$i'),
      );
    }
  }

  /// Clean old timestamps
  void _cleanOldTimestamps(String providerId) {
    final timestamps = _operationTimestamps[providerId];
    if (timestamps == null) return;

    final cutoff = DateTime.now().subtract(const Duration(minutes: 1));
    timestamps.removeWhere((t) => t.isBefore(cutoff));
  }

  /// Calculate severity of race condition
  String _calculateSeverity(String providerId, List<String> operations) {
    // Critical providers that affect core functionality
    if (providerId.contains('book') || providerId.contains('reading')) {
      return 'critical';
    }

    // High priority for user-facing state
    if (providerId.contains('ui') || providerId.contains('navigation')) {
      return 'high';
    }

    // Medium for data operations
    if (operations.length > 2) {
      return 'medium';
    }

    return 'low';
  }

  /// Generate description for race condition
  String _generateDescription(String providerId, List<String> operations) {
    return 'Concurrent operations detected in $providerId: ${operations.join(', ')}. '
        'This may lead to inconsistent state.';
  }

  /// Get detection history
  List<RaceConditionDetection> getDetectionHistory() {
    return _detectionHistory.toList();
  }

  /// Get detection stream
  Stream<RaceConditionDetection> get detectionStream => _detectionStream.stream;
}

/// State consistency validator
class StateConsistencyValidator {
  static StateConsistencyValidator? _instance;
  static StateConsistencyValidator get instance =>
      _instance ??= StateConsistencyValidator._();

  StateConsistencyValidator._();

  final Map<String, List<StateConsistencyRule>> _validationRules = {};
  final Queue<StateConsistencyResult> _validationHistory = Queue();

  /// Add validation rule for a provider
  void addRule(String providerId, StateConsistencyRule rule) {
    _validationRules.putIfAbsent(providerId, () => []).add(rule);
  }

  /// Validate state consistency
  StateConsistencyResult validateState(String providerId, dynamic state) {
    final rules = _validationRules[providerId] ?? [];
    final violations = <String>[];
    Map<String, dynamic>? suggestedFix;

    for (final rule in rules) {
      final result = rule.validate(state);
      if (!result.isValid) {
        violations.addAll(result.violations);
        if (result.suggestedFix != null) {
          suggestedFix ??= {};
          suggestedFix.addAll(result.suggestedFix!);
        }
      }
    }

    final result = StateConsistencyResult(
      providerId: providerId,
      isValid: violations.isEmpty,
      violations: violations,
      validatedAt: DateTime.now(),
      suggestedFix: suggestedFix,
    );

    _validationHistory.add(result);

    if (!result.isValid && kDebugMode) {
      AnxLog.warning(
        '❌ State consistency violation in $providerId: ${violations.join(', ')}',
      );
    }

    return result;
  }

  /// Get validation history
  List<StateConsistencyResult> getValidationHistory() {
    return _validationHistory.toList();
  }
}

/// State consistency validation rule
abstract class StateConsistencyRule {
  String get name;
  String get description;

  StateConsistencyResult validate(dynamic state);
}

/// State lock manager for coordinating updates
class StateLockManager {
  static StateLockManager? _instance;
  static StateLockManager get instance => _instance ??= StateLockManager._();

  StateLockManager._();

  final Map<String, StateLock> _activeLocks = {};
  Timer? _cleanupTimer;

  /// Initialize lock manager
  void initialize() {
    _cleanupTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _cleanupExpiredLocks();
    });
  }

  /// Acquire lock for provider operation
  Future<StateLock?> acquireLock(String providerId, String operationId) async {
    // Check for existing lock
    if (_activeLocks.containsKey(providerId)) {
      final existingLock = _activeLocks[providerId]!;
      if (!existingLock.isExpired) {
        if (kDebugMode) {
          debugPrint('🔒 Lock already held for $providerId');
        }
        return null; // Lock already held
      } else {
        // Remove expired lock
        _activeLocks.remove(providerId);
      }
    }

    // Create new lock
    final lock = StateLock(
      providerId: providerId,
      operationId: operationId,
      acquiredAt: DateTime.now(),
    );

    _activeLocks[providerId] = lock;

    if (kDebugMode) {
      debugPrint('🔓 Lock acquired for $providerId ($operationId)');
    }

    return lock;
  }

  /// Release lock
  void releaseLock(String providerId) {
    final lock = _activeLocks.remove(providerId);
    if (lock != null) {
      lock.release();
      if (kDebugMode) {
        debugPrint('🔓 Lock released for $providerId');
      }
    }
  }

  /// Check if provider is locked
  bool isLocked(String providerId) {
    final lock = _activeLocks[providerId];
    return lock != null && !lock.isExpired;
  }

  /// Clean up expired locks
  void _cleanupExpiredLocks() {
    final expiredKeys = _activeLocks.entries
        .where((entry) => entry.value.isExpired)
        .map((entry) => entry.key)
        .toList();

    for (final key in expiredKeys) {
      final lock = _activeLocks.remove(key);
      lock?.release();
      if (kDebugMode) {
        debugPrint('🧹 Cleaned up expired lock for $key');
      }
    }
  }

  /// Dispose resources
  void dispose() {
    _cleanupTimer?.cancel();
    for (final lock in _activeLocks.values) {
      lock.release();
    }
    _activeLocks.clear();
  }
}

/// Consistent state provider wrapper with built-in validation
class ConsistentStateProvider<T> extends StateNotifier<AsyncValue<T>> {
  final String providerId;
  final Future<T> Function() dataLoader;
  final List<StateConsistencyRule> validationRules;
  final Duration lockTimeout;

  ConsistentStateProvider({
    required this.providerId,
    required this.dataLoader,
    this.validationRules = const [],
    this.lockTimeout = const Duration(seconds: 30),
  }) : super(const AsyncValue.loading()) {
    // Register validation rules
    for (final rule in validationRules) {
      StateConsistencyValidator.instance.addRule(providerId, rule);
    }

    // Load initial data
    _loadData();
  }

  /// Load data with consistency checks
  Future<void> _loadData() async {
    final operationId = 'load_${DateTime.now().millisecondsSinceEpoch}';

    // Track operation for race condition detection
    RaceConditionDetector.instance.trackOperation(providerId, operationId);

    try {
      // Acquire lock
      final lock =
          await StateLockManager.instance.acquireLock(providerId, operationId);
      if (lock == null) {
        // Could not acquire lock, operation already in progress
        return;
      }

      try {
        final data = await dataLoader();

        // Validate state consistency
        final validationResult =
            StateConsistencyValidator.instance.validateState(providerId, data);

        if (validationResult.isValid) {
          state = AsyncValue.data(data);

          // Track successful state change
          StateFlowDebugger.instance.trackStateChange(
            providerId: providerId,
            providerType: 'ConsistentStateProvider',
            previousValue: state,
            newValue: data,
          );
        } else {
          // State validation failed
          final error = StateError(
            'State validation failed: ${validationResult.violations.join(', ')}',
          );
          state = AsyncValue.error(error, StackTrace.current);

          if (kDebugMode) {
            AnxLog.severe('State validation failed for $providerId', error);
          }
        }
      } finally {
        StateLockManager.instance.releaseLock(providerId);
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);

      if (kDebugMode) {
        AnxLog.severe('Error loading data for $providerId', error, stackTrace);
      }
    } finally {
      RaceConditionDetector.instance.completeOperation(providerId, operationId);
    }
  }

  /// Refresh data with consistency checks
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    await _loadData();
  }

  /// Update state with validation
  Future<void> updateState(T newState) async {
    final operationId = 'update_${DateTime.now().millisecondsSinceEpoch}';

    // Track operation
    RaceConditionDetector.instance.trackOperation(providerId, operationId);

    try {
      // Acquire lock
      final lock =
          await StateLockManager.instance.acquireLock(providerId, operationId);
      if (lock == null) {
        throw StateError('Could not acquire lock for state update');
      }

      try {
        // Validate new state
        final validationResult = StateConsistencyValidator.instance
            .validateState(providerId, newState);

        if (validationResult.isValid) {
          final previousState = state;
          state = AsyncValue.data(newState);

          // Track state change
          StateFlowDebugger.instance.trackStateChange(
            providerId: providerId,
            providerType: 'ConsistentStateProvider',
            previousValue: previousState,
            newValue: newState,
          );
        } else {
          throw StateError(
            'State validation failed: ${validationResult.violations.join(', ')}',
          );
        }
      } finally {
        StateLockManager.instance.releaseLock(providerId);
      }
    } finally {
      RaceConditionDetector.instance.completeOperation(providerId, operationId);
    }
  }
}

/// Book list consistency rule
class BookListConsistencyRule extends StateConsistencyRule {
  @override
  String get name => 'BookListConsistency';

  @override
  String get description => 'Validates book list state consistency';

  @override
  StateConsistencyResult validate(dynamic state) {
    final violations = <String>[];
    Map<String, dynamic>? suggestedFix;

    if (state is List<List<dynamic>>) {
      // Check for empty groups
      for (int i = 0; i < state.length; i++) {
        if (state[i].isEmpty) {
          violations.add('Empty book group at index $i');
        }
      }

      // Check for duplicate books
      final allBooks = state.expand((group) => group).toList();
      final bookIds = allBooks.map((book) => book.id).toSet();
      if (bookIds.length != allBooks.length) {
        violations.add('Duplicate books detected');
        suggestedFix = {'action': 'remove_duplicates'};
      }
    } else {
      violations.add('Invalid book list format');
    }

    return StateConsistencyResult(
      providerId: 'bookList',
      isValid: violations.isEmpty,
      violations: violations,
      validatedAt: DateTime.now(),
      suggestedFix: suggestedFix,
    );
  }
}

/// AI chat consistency rule
class AiChatConsistencyRule extends StateConsistencyRule {
  @override
  String get name => 'AiChatConsistency';

  @override
  String get description => 'Validates AI chat state consistency';

  @override
  StateConsistencyResult validate(dynamic state) {
    final violations = <String>[];

    if (state is List<dynamic>) {
      // Check message order
      for (int i = 1; i < state.length; i++) {
        final current = state[i];
        final previous = state[i - 1];

        if (current.timestamp != null && previous.timestamp != null) {
          if (current.timestamp.isBefore(previous.timestamp)) {
            violations.add('Messages not in chronological order');
            break;
          }
        }
      }

      // Check for alternating roles (if applicable)
      // This is a simplified check - adjust based on your AI chat requirements
    } else {
      violations.add('Invalid AI chat format');
    }

    return StateConsistencyResult(
      providerId: 'aiChat',
      isValid: violations.isEmpty,
      violations: violations,
      validatedAt: DateTime.now(),
    );
  }
}
