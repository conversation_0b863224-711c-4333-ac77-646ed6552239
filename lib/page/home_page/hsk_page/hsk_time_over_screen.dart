import 'package:dasso_reader/models/hsk_character_set.dart';
import 'package:dasso_reader/page/home_page/hsk_page/hsk_practice_screen.dart';
import 'package:dasso_reader/providers/hsk_providers.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/widgets/decorations/mountain_painter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class HskTimeOverScreen extends ConsumerStatefulWidget {
  final HskCharacterSet characterSet;
  final int correctAnswers;
  final int maxProgressPoints;
  final bool isTimeout;
  final bool endedDueToSuddenDeathMiss;
  final int actualProgressPoints;
  final int totalRoundsPlayed;
  final bool fullyCompletedSuccessfully;

  const HskTimeOverScreen({
    super.key,
    required this.characterSet,
    required this.correctAnswers,
    required this.maxProgressPoints,
    required this.isTimeout,
    required this.endedDueToSuddenDeathMiss,
    required this.actualProgressPoints,
    required this.totalRoundsPlayed,
    required this.fullyCompletedSuccessfully,
  });

  @override
  ConsumerState<HskTimeOverScreen> createState() => _HskTimeOverScreenState();
}

class _HskTimeOverScreenState extends ConsumerState<HskTimeOverScreen> {
  int _sessionDurationSeconds = 0;
  bool _initialized = false;

  // =====================================================
  // STATIC OPTIMIZED COMPONENTS - Performance Enhancement
  // =====================================================

  /// Theme-aware background gradient with WCAG AAA compliance
  BoxDecoration _getBackgroundGradient(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          colorScheme.primaryContainer,
          colorScheme.secondaryContainer,
        ],
      ),
    );
  }

  /// Theme-aware title text style with WCAG AAA compliance
  TextStyle _getTitleTextStyle(BuildContext context) => TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.bold,
        color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
        shadows: const [
          Shadow(
            offset: Offset(1, 1),
            blurRadius: 3.0,
            color: Color.fromARGB(150, 0, 0, 0),
          ),
        ],
      );

  /// Theme-aware statistics container decoration
  BoxDecoration _getStatsContainerDecoration(BuildContext context) =>
      BoxDecoration(
        color: Theme.of(context)
            .colorScheme
            .surfaceContainerHighest
            .withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(DesignSystem.radiusM), // 16.0
      );

  /// Theme-aware text styles with WCAG AAA compliance
  TextStyle _getStatsLabelStyle(BuildContext context) => TextStyle(
        color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
      );

  TextStyle _getStatsValueStyle(BuildContext context) => TextStyle(
        color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
        fontWeight: FontWeight.bold,
      );

  /// Theme-aware button colors with WCAG AAA compliance
  Color _getReturnButtonColor(BuildContext context) =>
      Theme.of(context).colorScheme.surfaceContainerHigh;
  Color _getAgainButtonColor(BuildContext context) =>
      Theme.of(context).colorScheme.secondary;

  /// Theme-aware button styling with WCAG AAA compliance
  ButtonStyle _getButtonStyle(BuildContext context) => ElevatedButton.styleFrom(
        foregroundColor:
            DesignSystem.getSettingsTextColor(context, isPrimary: true),
        padding: const EdgeInsets.symmetric(
          horizontal: DesignSystem.spaceXL, // 32.0
          vertical: DesignSystem.spaceM, // 16.0
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusM - 4), // 12.0
        ),
      );

  /// Static button text style for better performance
  static const TextStyle _buttonTextStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.bold,
  );

  /// Static spacing constants using DesignSystem
  static double get _mainPadding => DesignSystem.spaceL + 4; // 24.0
  static double get _titleSpacing => DesignSystem.spaceXL + 16; // 48.0
  static double get _statsSpacing => DesignSystem.spaceS; // 8.0
  static double get _buttonSpacing => DesignSystem.spaceXL * 2; // 64.0
  static EdgeInsets get _statsContainerPadding => const EdgeInsets.symmetric(
        horizontal: DesignSystem.spaceL + 4, // 24.0
        vertical: DesignSystem.spaceM, // 16.0
      );

  @override
  void initState() {
    super.initState();
    // Schedule getting the duration after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Use a delayed call to prevent modifying during build
      Future.microtask(() {
        if (mounted) {
          final duration = ref
              .read(hskSessionProgressProvider.notifier)
              .getSessionDurationSeconds();
          setState(() {
            _sessionDurationSeconds = duration;
            _initialized = true;
          });
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    // Simplified to only two screens as per requirements
    final String titleText =
        widget.isTimeout ? 'Time Over!' : 'Practice Complete';

    return Scaffold(
      body: Container(
        decoration: _getBackgroundGradient(context),
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.all(_mainPadding),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Title
                Text(
                  titleText,
                  style: _getTitleTextStyle(context),
                ),

                SizedBox(height: _titleSpacing),

                // Simplified statistics as per requirements
                Container(
                  padding: _statsContainerPadding,
                  decoration: _getStatsContainerDecoration(context),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Completed:',
                            style: _getStatsLabelStyle(context),
                          ),
                          Text(
                            _getProgressDisplayText(),
                            style: _getStatsValueStyle(context),
                          ),
                        ],
                      ),
                      SizedBox(height: _statsSpacing),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Time spent:',
                            style: _getStatsLabelStyle(context),
                          ),
                          Text(
                            _formatTime(_sessionDurationSeconds),
                            style: _getStatsValueStyle(context),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                SizedBox(height: _buttonSpacing),

                // Buttons - optimized with static styling
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        // Pop back to HskSetDetailsScreen (consistent with Learn mode)
                        Navigator.pop(context);
                      },
                      style: _getButtonStyle(context).copyWith(
                        backgroundColor: WidgetStateProperty.all(
                          _getReturnButtonColor(context),
                        ),
                      ),
                      child: const Text(
                        'Return',
                        style: _buttonTextStyle,
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                            builder: (context) => HskPracticeScreen(
                              characterSet: widget.characterSet,
                            ),
                          ),
                        );
                      },
                      style: _getButtonStyle(context).copyWith(
                        backgroundColor: WidgetStateProperty.all(
                          _getAgainButtonColor(context),
                        ),
                      ),
                      child: const Text(
                        'Again',
                        style: _buttonTextStyle,
                      ),
                    ),
                  ],
                ),

                const Spacer(),

                // Mountain silhouette decoration - optimized with existing widget
                Container(
                  height: 100,
                  width: double.infinity,
                  alignment: Alignment.bottomCenter,
                  child: const MountainDecoration(
                    height: 80,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Returns the appropriate progress display text based on completion status
  String _getProgressDisplayText() {
    if (widget.fullyCompletedSuccessfully) {
      // Successfully completed everything - show full completion
      return '${widget.actualProgressPoints}/${widget.maxProgressPoints}';
    } else if (widget.isTimeout) {
      // Timeout occurred - show actual progress made
      return '${widget.actualProgressPoints}/${widget.maxProgressPoints}';
    } else if (widget.endedDueToSuddenDeathMiss) {
      // Sudden death miss - show actual progress made
      return '${widget.actualProgressPoints}/${widget.maxProgressPoints}';
    } else {
      // Fallback - show actual progress
      return '${widget.actualProgressPoints}/${widget.maxProgressPoints}';
    }
  }

  String _formatTime(int seconds) {
    if (seconds < 60) {
      return '$seconds sec';
    } else if (seconds < 3600) {
      final minutes = seconds ~/ 60;
      final remainingSeconds = seconds % 60;
      return "$minutes min ${remainingSeconds > 0 ? '$remainingSeconds sec' : ''}";
    } else {
      final hours = seconds ~/ 3600;
      final minutes = (seconds % 3600) ~/ 60;
      return "$hours h ${minutes > 0 ? '$minutes min' : ''}";
    }
  }
}
