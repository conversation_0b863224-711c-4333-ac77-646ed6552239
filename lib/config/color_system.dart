import 'package:flutter/material.dart';
import 'design_system.dart';

// =====================================================
// COLOR SCHEME CLASSES FOR SPECIALIZED COMPONENTS
// =====================================================

/// Color scheme for HSK learning components
class HSKComponentColors {
  final Color appBarBackground;
  final Color progressValue;
  final Color progressBackground;
  final Color promptArea;
  final Color buttonHighlight;
  final Color buttonPressed;
  final Color buttonSelection;
  final Color correctStart;
  final Color correctEnd;
  final Color wrongStart;
  final Color wrongEnd;
  final Color audioError;

  const HSKComponentColors({
    required this.appBarBackground,
    required this.progressValue,
    required this.progressBackground,
    required this.promptArea,
    required this.buttonHighlight,
    required this.buttonPressed,
    required this.buttonSelection,
    required this.correctStart,
    required this.correctEnd,
    required this.wrongStart,
    required this.wrongEnd,
    required this.audioError,
  });
}

/// Color scheme for context menu components
class ContextMenuColors {
  final Color background;
  final Color border;
  final Color selectedBorder;
  final Color iconPrimary;
  final Color iconSecondary;
  final Color iconDisabled;

  const ContextMenuColors({
    required this.background,
    required this.border,
    required this.selectedBorder,
    required this.iconPrimary,
    required this.iconSecondary,
    required this.iconDisabled,
  });
}

/// Color scheme for reading components
class ReadingComponentColors {
  final Color tabIcon;
  final Color tabBackground;
  final Color modalOverlay;
  final Color controlBackground;

  const ReadingComponentColors({
    required this.tabIcon,
    required this.tabBackground,
    required this.modalOverlay,
    required this.controlBackground,
  });
}

/// Extended color system for the Dasso Reader app
///
/// This class provides additional color utilities, semantic colors,
/// and accessibility-focused color helpers to complement the main DesignSystem.
class ColorSystem {
  // Private constructor to prevent instantiation
  ColorSystem._();

  // =====================================================
  // SEMANTIC COLORS - Context-specific color meanings
  // =====================================================

  /// Information colors - for informational messages and hints
  static const Color infoColor = Color(0xFF2196F3); // Blue
  static const Color infoColorLight = Color(0xFF64B5F6);
  static const Color infoColorDark = Color(0xFF1976D2);

  /// Neutral colors - for dividers, borders, and subtle elements
  static const Color neutralColor = Color(0xFF9E9E9E);
  static const Color neutralColorLight = Color(0xFFE0E0E0);
  static const Color neutralColorDark = Color(0xFF616161);

  /// Reading progress colors
  static const Color progressColor = DesignSystem.primaryColor;
  static const Color progressBackgroundColor = Color(0xFFE3F2FD);
  static const Color progressCompletedColor = DesignSystem.successColor;

  /// Dictionary and learning colors
  static const Color dictionaryHighlight = Color(0xFFFFEB3B); // Yellow
  static const Color vocabularyKnown = DesignSystem.successColor;
  static const Color vocabularyLearning = DesignSystem.warningColor;
  static const Color vocabularyUnknown = DesignSystem.errorColor;

  /// HSK level colors - for Chinese learning levels
  static const Map<int, Color> hskLevelColors = {
    1: Color(0xFF4CAF50), // Green - Beginner
    2: Color(0xFF8BC34A), // Light Green
    3: Color(0xFFFFEB3B), // Yellow
    4: Color(0xFFFF9800), // Orange
    5: Color(0xFFFF5722), // Deep Orange
    6: Color(0xFFF44336), // Red - Advanced
  };

  // =====================================================
  // ACCESSIBILITY HELPERS
  // =====================================================

  /// Get high contrast color pair for accessibility
  static ColorPair getHighContrastPair(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    if (isDark) {
      return const ColorPair(
        foreground: DesignSystem.darkTextColorPrimary,
        background: DesignSystem.darkSurfaceColor,
      );
    }
    return const ColorPair(
      foreground: DesignSystem.textColorPrimary,
      background: DesignSystem.surfaceColor,
    );
  }

  /// Get color with sufficient contrast against background
  static Color getContrastingColor(Color background, {bool preferDark = true}) {
    final backgroundLuminance = background.computeLuminance();

    // If background is light, use dark text; if dark, use light text
    if (backgroundLuminance > 0.5) {
      return preferDark ? DesignSystem.textColorPrimary : Colors.black;
    } else {
      return preferDark ? Colors.white : DesignSystem.darkTextColorPrimary;
    }
  }

  /// Validate and adjust color for accessibility compliance
  static Color ensureAccessibleColor(Color foreground, Color background) {
    if (DesignSystem.hasValidContrast(foreground, background)) {
      return foreground;
    }

    // If contrast is insufficient, return a high-contrast alternative
    return getContrastingColor(background);
  }

  // =====================================================
  // THEME-AWARE COLOR GETTERS
  // =====================================================

  /// Get appropriate card color based on theme
  static Color getCardColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark
        ? DesignSystem.darkSurfaceColorVariant
        : DesignSystem.surfaceColor;
  }

  /// Get appropriate divider color based on theme
  static Color getDividerColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark
        ? DesignSystem.darkTextColorHint.withValues(alpha: 0.3)
        : DesignSystem.textColorHint.withValues(alpha: 0.3);
  }

  /// Get appropriate border color based on theme
  static Color getBorderColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark
        ? DesignSystem.darkTextColorHint.withValues(alpha: 0.5)
        : DesignSystem.textColorHint.withValues(alpha: 0.5);
  }

  /// Get appropriate surface color based on theme
  static Color getSurfaceColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? DesignSystem.darkSurfaceColor : DesignSystem.surfaceColor;
  }

  /// Get appropriate background color based on theme
  static Color getBackgroundColor(BuildContext context) {
    return Theme.of(context).colorScheme.surface;
  }

  /// Get appropriate app bar color based on theme
  static Color getAppBarColor(BuildContext context) {
    return Theme.of(context).colorScheme.surface;
  }

  /// Get appropriate scaffold background color based on theme
  static Color getScaffoldBackgroundColor(BuildContext context) {
    return Theme.of(context).colorScheme.surface;
  }

  /// Get appropriate overlay color based on theme
  static Color getOverlayColor(BuildContext context, {double opacity = 0.5}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark
        ? Colors.black.withValues(alpha: opacity)
        : Colors.black.withValues(alpha: opacity * 0.7);
  }

  /// Get appropriate shadow color based on theme
  static Color getShadowColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark
        ? Colors.black.withValues(alpha: 0.3)
        : Colors.black.withValues(alpha: 0.1);
  }

  // =====================================================
  // READING EXPERIENCE COLORS
  // =====================================================

  /// Get reading theme colors based on current reading theme
  static ReadingColorScheme getReadingColors(
    String backgroundColor,
    String textColor,
  ) {
    final bgColor = Color(int.parse('0x$backgroundColor'));
    final txtColor = Color(int.parse('0x$textColor'));

    return ReadingColorScheme(
      background: bgColor,
      text: txtColor,
      accent: _getReadingAccentColor(bgColor),
      selection: txtColor.withValues(alpha: 0.3),
      highlight: DesignSystem.highlightColor,
    );
  }

  /// Get appropriate accent color for reading theme
  static Color _getReadingAccentColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();

    // For dark backgrounds, use lighter accent
    if (luminance < 0.5) {
      return DesignSystem.primaryColorLight;
    }
    // For light backgrounds, use darker accent
    return DesignSystem.primaryColorDark;
  }

  // =====================================================
  // STATE-BASED COLORS
  // =====================================================

  /// Get color for different UI states
  static Color getStateColor(UIState state, {bool isDark = false}) {
    switch (state) {
      case UIState.normal:
        return isDark
            ? DesignSystem.darkTextColorPrimary
            : DesignSystem.textColorPrimary;
      case UIState.hover:
        return DesignSystem.interactiveColorHover;
      case UIState.pressed:
        return DesignSystem.interactiveColorPressed;
      case UIState.focused:
        return DesignSystem.focusColor;
      case UIState.disabled:
        return DesignSystem.textColorDisabled;
      case UIState.selected:
        return DesignSystem.primaryColor;
      case UIState.error:
        return DesignSystem.errorColor;
      case UIState.success:
        return DesignSystem.successColor;
      case UIState.warning:
        return DesignSystem.warningColor;
    }
  }

  // =====================================================
  // COMPONENT-SPECIFIC COLOR HELPERS
  // =====================================================

  /// Get standardized button colors based on button type
  static ButtonColorScheme getButtonColors(
    BuildContext context,
    ButtonType type,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    switch (type) {
      case ButtonType.primary:
        return ButtonColorScheme(
          background: colorScheme.primary,
          foreground: colorScheme.onPrimary,
          disabled: DesignSystem.interactiveColorDisabled,
          hover: DesignSystem.interactiveColorHover,
          pressed: DesignSystem.interactiveColorPressed,
        );
      case ButtonType.secondary:
        return ButtonColorScheme(
          background: colorScheme.secondary,
          foreground: colorScheme.onSecondary,
          disabled: DesignSystem.interactiveColorDisabled,
          hover: DesignSystem.secondaryColorLight,
          pressed: DesignSystem.secondaryColorDark,
        );
      case ButtonType.success:
        return ButtonColorScheme(
          background: DesignSystem.successColor,
          foreground: colorScheme.onPrimary,
          disabled: DesignSystem.interactiveColorDisabled,
          hover: DesignSystem.successColorLight,
          pressed: DesignSystem.successColorDark,
        );
      case ButtonType.warning:
        return ButtonColorScheme(
          background: DesignSystem.warningColor,
          foreground: colorScheme.onPrimary,
          disabled: DesignSystem.interactiveColorDisabled,
          hover: DesignSystem.warningColorLight,
          pressed: DesignSystem.warningColorDark,
        );
      case ButtonType.error:
        return ButtonColorScheme(
          background: DesignSystem.errorColor,
          foreground: colorScheme.onPrimary,
          disabled: DesignSystem.interactiveColorDisabled,
          hover: DesignSystem.errorColorLight,
          pressed: DesignSystem.errorColorDark,
        );
      case ButtonType.neutral:
        return ButtonColorScheme(
          background:
              isDark ? DesignSystem.darkSurfaceColorVariant : neutralColorLight,
          foreground: isDark
              ? DesignSystem.darkTextColorPrimary
              : DesignSystem.textColorPrimary,
          disabled: DesignSystem.interactiveColorDisabled,
          hover: isDark ? DesignSystem.darkTextColorHint : neutralColor,
          pressed:
              isDark ? DesignSystem.darkTextColorSecondary : neutralColorDark,
        );
    }
  }

  /// Get standardized HSK level colors (replacing hardcoded colors)
  static Color getHskLevelColor(int level) {
    return hskLevelColors[level] ?? neutralColor;
  }

  /// Get standardized icon colors based on context
  static Color getIconColor(BuildContext context, IconType type) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    switch (type) {
      case IconType.primary:
        return colorScheme.primary;
      case IconType.secondary:
        return isDark
            ? DesignSystem.darkTextColorSecondary
            : DesignSystem.textColorSecondary;
      case IconType.disabled:
        return DesignSystem.textColorDisabled;
      case IconType.error:
        return DesignSystem.errorColor;
      case IconType.success:
        return DesignSystem.successColor;
      case IconType.warning:
        return DesignSystem.warningColor;
      case IconType.info:
        return infoColor;
    }
  }

  // =====================================================
  // SPECIALIZED COMPONENT COLORS
  // =====================================================

  /// Get theme-aware colors for HSK learning components
  static HSKComponentColors getHSKColors(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return HSKComponentColors(
      appBarBackground:
          isDark ? DesignSystem.primaryColorDark : DesignSystem.primaryColor,
      progressValue:
          isDark ? DesignSystem.warningColorLight : DesignSystem.warningColor,
      progressBackground: isDark
          ? DesignSystem.darkSurfaceColorVariant.withValues(alpha: 0.3)
          : DesignSystem.surfaceColorVariant.withValues(alpha: 0.3),
      promptArea:
          isDark ? DesignSystem.primaryColorDark : DesignSystem.primaryColor,
      buttonHighlight: isDark
          ? DesignSystem.darkSurfaceColorVariant.withValues(alpha: 0.3)
          : DesignSystem.surfaceColorVariant.withValues(alpha: 0.3),
      buttonPressed: isDark
          ? DesignSystem.darkSurfaceColorVariant.withValues(alpha: 0.5)
          : DesignSystem.surfaceColorVariant.withValues(alpha: 0.5),
      buttonSelection: isDark
          ? DesignSystem.primaryColorLight.withValues(alpha: 0.4)
          : DesignSystem.primaryColor.withValues(alpha: 0.4),
      correctStart: DesignSystem.successColor.withValues(alpha: 0.3),
      correctEnd: DesignSystem.successColor.withValues(alpha: 0.6),
      wrongStart: DesignSystem.errorColor.withValues(alpha: 0.3),
      wrongEnd: DesignSystem.errorColor.withValues(alpha: 0.6),
      audioError: DesignSystem.errorColor.withValues(alpha: 0.7),
    );
  }

  /// Get theme-aware colors for context menu components
  static ContextMenuColors getContextMenuColors(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return ContextMenuColors(
      background: isDark
          ? DesignSystem.darkSurfaceColorVariant
          : DesignSystem.surfaceColor,
      border: getBorderColor(context),
      selectedBorder: colorScheme.primary,
      iconPrimary: colorScheme.primary,
      iconSecondary: isDark
          ? DesignSystem.darkTextColorSecondary
          : DesignSystem.textColorSecondary,
      iconDisabled: DesignSystem.textColorDisabled,
    );
  }

  /// Get theme-aware colors for reading components
  static ReadingComponentColors getReadingComponentColors(
    BuildContext context,
  ) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return ReadingComponentColors(
      tabIcon: isDark
          ? DesignSystem.darkTextColorPrimary
          : DesignSystem.textColorPrimary,
      tabBackground:
          isDark ? DesignSystem.darkSurfaceColor : DesignSystem.surfaceColor,
      modalOverlay: getOverlayColor(context),
      controlBackground: isDark
          ? DesignSystem.darkSurfaceColorVariant
          : DesignSystem.surfaceColor,
    );
  }

  /// Get standardized text colors based on context
  static Color getTextColor(BuildContext context, TextType type) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    switch (type) {
      case TextType.primary:
        return isDark
            ? DesignSystem.darkTextColorPrimary
            : DesignSystem.textColorPrimary;
      case TextType.secondary:
        return isDark
            ? DesignSystem.darkTextColorSecondary
            : DesignSystem.textColorSecondary;
      case TextType.hint:
        return isDark
            ? DesignSystem.darkTextColorHint
            : DesignSystem.textColorHint;
      case TextType.disabled:
        return DesignSystem.textColorDisabled;
      case TextType.error:
        return DesignSystem.errorColor;
      case TextType.success:
        return DesignSystem.successColor;
      case TextType.warning:
        return DesignSystem.warningColor;
      case TextType.link:
        return DesignSystem.primaryColor;
    }
  }

  // =====================================================
  // STATUS MESSAGE HELPERS
  // =====================================================

  /// Get standardized colors for status messages (toasts, snackbars, dialogs)
  static StatusColorScheme getStatusColors(
    BuildContext context,
    StatusType type,
  ) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final surfaceColor =
        isDark ? DesignSystem.darkSurfaceColor : DesignSystem.surfaceColor;

    switch (type) {
      case StatusType.info:
        return StatusColorScheme(
          background: infoColor.withValues(alpha: 0.1),
          foreground: infoColor,
          icon: infoColor,
          border: infoColor.withValues(alpha: 0.3),
        );
      case StatusType.success:
        return StatusColorScheme(
          background: DesignSystem.successColor.withValues(alpha: 0.1),
          foreground: DesignSystem.successColor,
          icon: DesignSystem.successColor,
          border: DesignSystem.successColor.withValues(alpha: 0.3),
        );
      case StatusType.warning:
        return StatusColorScheme(
          background: DesignSystem.warningColor.withValues(alpha: 0.1),
          foreground: DesignSystem.warningColor,
          icon: DesignSystem.warningColor,
          border: DesignSystem.warningColor.withValues(alpha: 0.3),
        );
      case StatusType.error:
        return StatusColorScheme(
          background: DesignSystem.errorColor.withValues(alpha: 0.1),
          foreground: DesignSystem.errorColor,
          icon: DesignSystem.errorColor,
          border: DesignSystem.errorColor.withValues(alpha: 0.3),
        );
      case StatusType.neutral:
        return StatusColorScheme(
          background: surfaceColor,
          foreground: isDark
              ? DesignSystem.darkTextColorPrimary
              : DesignSystem.textColorPrimary,
          icon: isDark
              ? DesignSystem.darkTextColorSecondary
              : DesignSystem.textColorSecondary,
          border: isDark
              ? DesignSystem.darkTextColorHint
              : DesignSystem.textColorHint,
        );
    }
  }

  /// Get standardized colors for form validation states
  static ValidationColorScheme getValidationColors(
    BuildContext context,
    ValidationState state,
  ) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    switch (state) {
      case ValidationState.normal:
        return ValidationColorScheme(
          border: isDark
              ? DesignSystem.darkTextColorHint
              : DesignSystem.textColorHint,
          background: isDark
              ? DesignSystem.darkSurfaceColor
              : DesignSystem.surfaceColor,
          text: isDark
              ? DesignSystem.darkTextColorPrimary
              : DesignSystem.textColorPrimary,
          helper: isDark
              ? DesignSystem.darkTextColorSecondary
              : DesignSystem.textColorSecondary,
        );
      case ValidationState.focused:
        return ValidationColorScheme(
          border: DesignSystem.focusColor,
          background: isDark
              ? DesignSystem.darkSurfaceColor
              : DesignSystem.surfaceColor,
          text: isDark
              ? DesignSystem.darkTextColorPrimary
              : DesignSystem.textColorPrimary,
          helper: isDark
              ? DesignSystem.darkTextColorSecondary
              : DesignSystem.textColorSecondary,
        );
      case ValidationState.error:
        return ValidationColorScheme(
          border: DesignSystem.errorColor,
          background: DesignSystem.errorColor.withValues(alpha: 0.05),
          text: DesignSystem.errorColor,
          helper: DesignSystem.errorColor,
        );
      case ValidationState.success:
        return ValidationColorScheme(
          border: DesignSystem.successColor,
          background: DesignSystem.successColor.withValues(alpha: 0.05),
          text: isDark
              ? DesignSystem.darkTextColorPrimary
              : DesignSystem.textColorPrimary,
          helper: DesignSystem.successColor,
        );
      case ValidationState.disabled:
        return ValidationColorScheme(
          border: DesignSystem.textColorDisabled,
          background: DesignSystem.textColorDisabled.withValues(alpha: 0.05),
          text: DesignSystem.textColorDisabled,
          helper: DesignSystem.textColorDisabled,
        );
    }
  }
}

// =====================================================
// HELPER CLASSES
// =====================================================

/// Color pair for foreground/background combinations
class ColorPair {
  final Color foreground;
  final Color background;

  const ColorPair({
    required this.foreground,
    required this.background,
  });

  /// Check if this color pair has sufficient contrast
  bool get hasValidContrast =>
      DesignSystem.hasValidContrast(foreground, background);
}

/// Reading color scheme for reading experience
class ReadingColorScheme {
  final Color background;
  final Color text;
  final Color accent;
  final Color selection;
  final Color highlight;

  const ReadingColorScheme({
    required this.background,
    required this.text,
    required this.accent,
    required this.selection,
    required this.highlight,
  });
}

/// UI state enumeration for state-based coloring
enum UIState {
  normal,
  hover,
  pressed,
  focused,
  disabled,
  selected,
  error,
  success,
  warning,
}

/// Button type enumeration for consistent button styling
enum ButtonType {
  primary,
  secondary,
  success,
  warning,
  error,
  neutral,
}

/// Icon type enumeration for consistent icon coloring
enum IconType {
  primary,
  secondary,
  disabled,
  error,
  success,
  warning,
  info,
}

/// Text type enumeration for consistent text coloring
enum TextType {
  primary,
  secondary,
  hint,
  disabled,
  error,
  success,
  warning,
  link,
}

/// Button color scheme for consistent button styling
class ButtonColorScheme {
  final Color background;
  final Color foreground;
  final Color disabled;
  final Color hover;
  final Color pressed;

  const ButtonColorScheme({
    required this.background,
    required this.foreground,
    required this.disabled,
    required this.hover,
    required this.pressed,
  });
}

/// Status type enumeration for status messages
enum StatusType {
  info,
  success,
  warning,
  error,
  neutral,
}

/// Validation state enumeration for form validation
enum ValidationState {
  normal,
  focused,
  error,
  success,
  disabled,
}

/// Status color scheme for status messages (toasts, snackbars, dialogs)
class StatusColorScheme {
  final Color background;
  final Color foreground;
  final Color icon;
  final Color border;

  const StatusColorScheme({
    required this.background,
    required this.foreground,
    required this.icon,
    required this.border,
  });
}

/// Validation color scheme for form validation states
class ValidationColorScheme {
  final Color border;
  final Color background;
  final Color text;
  final Color helper;

  const ValidationColorScheme({
    required this.border,
    required this.background,
    required this.text,
    required this.helper,
  });
}
