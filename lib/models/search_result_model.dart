class SearchResultModel {
  final String label;
  final String cfi;
  final List<SearchResultSubitemModel> subitems;

  SearchResultModel({
    required this.label,
    required this.cfi,
    required this.subitems,
  });

  static SearchResultModel fromJson(Map<String, dynamic> search) {
    return SearchResultModel(
      label: search['label'] as String? ?? '',
      cfi: search['cfi'] as String? ?? '',
      subitems: (search['subitems'] as List<dynamic>? ?? [])
          .map<SearchResultSubitemModel>(
            (subitem) => SearchResultSubitemModel.fromJson(
                subitem as Map<String, dynamic>),
          )
          .toList(),
    );
  }
}

class SearchResultSubitemModel {
  final String cfi;
  final String pre;
  final String match;
  final String post;

  SearchResultSubitemModel({
    required this.cfi,
    required this.pre,
    required this.match,
    required this.post,
  });

  static SearchResultSubitemModel fromJson(Map<String, dynamic> subitem) {
    final excerpt = subitem['excerpt'] as Map<String, dynamic>? ?? {};
    return SearchResultSubitemModel(
      cfi: subitem['cfi'] as String? ?? '',
      pre: excerpt['pre'] as String? ?? '',
      match: excerpt['match'] as String? ?? '',
      post: excerpt['post'] as String? ?? '',
    );
  }
}
