import 'package:dasso_reader/main.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/models/search_result_model.dart';
import 'package:dasso_reader/page/book_player/epub_player.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Define the StateProvider for the active search term
final activeSearchTermProvider = StateProvider<String?>((ref) => null);

class SearchPage extends ConsumerStatefulWidget {
  final Function hideAppBarAndBottomBar;
  final GlobalKey<EpubPlayerState> epubPlayerKey;
  final Book book;
  final Color? backgroundColor;
  final Color? textColor;

  const SearchPage({
    super.key,
    required this.hideAppBarAndBottomBar,
    required this.epubPlayerKey,
    required this.book,
    this.backgroundColor,
    this.textColor,
  });

  @override
  ConsumerState<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends ConsumerState<SearchPage> {
  String? _searchValue;
  late TextEditingController searchBarController;

  @override
  void initState() {
    super.initState();
    // Read the initial search term from the provider
    _searchValue = ref.read(activeSearchTermProvider);
    searchBarController = TextEditingController(text: _searchValue);

    // If there's an existing search term, trigger the search again
    // as the player state might have been cleared or page reloaded.
    // Add a small delay to ensure the player is ready.
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_searchValue != null && _searchValue!.isNotEmpty) {
        widget.epubPlayerKey.currentState?.search(_searchValue!);
      } else {
        // Ensure highlights are cleared if the persisted term is null/empty
        widget.epubPlayerKey.currentState?.clearSearch();
      }
    });
  }

  @override
  void dispose() {
    searchBarController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Read the current search term from the provider to react to external changes if needed
    // Although in this flow, changes mainly originate from this page.
    // We use watch here mainly to rebuild if the provider was cleared externally (less likely).
    final currentSearchTerm = ref.watch(activeSearchTermProvider);
    // Sync local state if provider changes externally (e.g., future global clear button)
    if (_searchValue != currentSearchTerm) {
      setState(() {
        _searchValue = currentSearchTerm;
        searchBarController.text = _searchValue ?? '';
        // Re-trigger search or clear based on the new provider state
        if (_searchValue != null && _searchValue!.isNotEmpty) {
          widget.epubPlayerKey.currentState?.search(_searchValue!);
        } else {
          widget.epubPlayerKey.currentState?.clearSearch();
        }
      });
    }

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          // Don't clear the search term when just popping the page
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        backgroundColor: widget.backgroundColor,
        appBar: AppBar(
          backgroundColor: widget.backgroundColor,
          foregroundColor: widget.textColor,
          title: const Text('Search'),
          leading: SemanticHelpers.button(
            context: context,
            label: 'Back to Reading',
            hint: 'Return to the book reading page',
            onTap: () {
              Navigator.of(context).pop();
            },
            child: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ),
          elevation: 0,
        ),
        body: Column(
          children: [
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: SizedBox(
                height: 50,
                child: SearchBar(
                  controller: searchBarController,
                  shadowColor:
                      const WidgetStatePropertyAll<Color>(Colors.transparent),
                  padding: const WidgetStatePropertyAll<EdgeInsets>(
                    EdgeInsets.symmetric(horizontal: 16.0),
                  ),
                  leading: Icon(Icons.search, color: widget.textColor),
                  trailing: [
                    _searchValue != null && _searchValue!.isNotEmpty
                        ? SemanticHelpers.button(
                            context: context,
                            label: 'Clear Search',
                            hint: 'Clear the current search term and results',
                            onTap: () {
                              setState(() {
                                _searchValue = null;
                                searchBarController.clear();
                                ref
                                    .read(activeSearchTermProvider.notifier)
                                    .state = null;
                                widget.epubPlayerKey.currentState!
                                    .clearSearch();
                              });
                            },
                            child: IconButton(
                              icon: Icon(Icons.close, color: widget.textColor),
                              onPressed: () {
                                setState(() {
                                  _searchValue = null;
                                  searchBarController.clear();
                                  ref
                                      .read(activeSearchTermProvider.notifier)
                                      .state = null;
                                  widget.epubPlayerKey.currentState!
                                      .clearSearch();
                                });
                              },
                            ),
                          )
                        : const SizedBox(),
                  ],
                  hintText: 'Search in book',
                  hintStyle: WidgetStatePropertyAll(
                    TextStyle(color: widget.textColor?.withValues(alpha: 0.5)),
                  ),
                  onSubmitted: (value) {
                    final trimmedValue = value.trim();
                    setState(() {
                      if (trimmedValue.isEmpty) {
                        _searchValue = null;
                        ref.read(activeSearchTermProvider.notifier).state =
                            null;
                        widget.epubPlayerKey.currentState!.clearSearch();
                      } else {
                        _searchValue = trimmedValue;
                        ref.read(activeSearchTermProvider.notifier).state =
                            trimmedValue;
                        widget.epubPlayerKey.currentState!.search(trimmedValue);
                      }
                    });
                  },
                ),
              ),
            ),
            StreamBuilder<double>(
              stream: widget.epubPlayerKey.currentState!.searchProgressStream,
              builder: (context, snapshot) {
                return snapshot.data == 1.0
                    ? const SizedBox()
                    : LinearProgressIndicator(
                        value: snapshot.data ?? 0.0,
                      );
              },
            ),
            Expanded(
              child: StreamBuilder<List<SearchResultModel>>(
                stream: widget.epubPlayerKey.currentState!.searchResultStream,
                builder: (context, snapshot) {
                  if (_searchValue == null || snapshot.data == null) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search,
                            size: 48,
                            color: widget.textColor?.withValues(alpha: 0.3),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Enter text to search',
                            style: TextStyle(
                              color: widget.textColor?.withValues(alpha: 0.5),
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  List<SearchResultModel> searchResults = snapshot.data!;
                  if (searchResults.isEmpty ||
                      searchResults.every((r) => r.subitems.isEmpty)) {
                    return Center(
                      child: Text(
                        'No results found',
                        style: TextStyle(color: widget.textColor),
                      ),
                    );
                  }

                  List<Map<String, dynamic>> flatResults = [];
                  for (var result in searchResults) {
                    for (var subitem in result.subitems) {
                      flatResults.add({
                        'label': result.label,
                        'subitem': subitem,
                      });
                    }
                  }

                  return ListView.builder(
                    itemCount: flatResults.length,
                    itemBuilder: (context, index) {
                      final resultData = flatResults[index];
                      final String chapterLabel = resultData['label'];
                      final SearchResultSubitemModel subitem =
                          resultData['subitem'];
                      final TextStyle matchStyle = TextStyle(
                        color: Theme.of(navigatorKey.currentContext!)
                            .colorScheme
                            .primary,
                        fontWeight: FontWeight.bold,
                      );
                      final TextStyle prePostStyle = TextStyle(
                        color: widget.textColor?.withValues(alpha: 0.7) ??
                            Colors.grey,
                      );
                      final TextStyle chapterStyle = TextStyle(
                        color: widget.textColor?.withValues(alpha: 0.5) ??
                            Colors.grey[600],
                        fontSize: 12,
                      );

                      return SemanticHelpers.button(
                        context: context,
                        label:
                            'Search result: ${subitem.pre}${subitem.match}${subitem.post}',
                        hint:
                            'Navigate to this search result in chapter: $chapterLabel',
                        onTap: () {
                          Navigator.of(context).pop();
                          widget.epubPlayerKey.currentState!.goToCfi(
                            subitem.cfi,
                          );
                          widget.hideAppBarAndBottomBar(false);
                        },
                        child: ListTile(
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16.0,
                            vertical: 4.0,
                          ),
                          title: RichText(
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: subitem.pre,
                                  style: prePostStyle,
                                ),
                                TextSpan(
                                  text: subitem.match,
                                  style: matchStyle,
                                ),
                                TextSpan(
                                  text: subitem.post,
                                  style: prePostStyle,
                                ),
                              ],
                            ),
                          ),
                          subtitle: Padding(
                            padding: const EdgeInsets.only(top: 4.0),
                            child: Text(
                              chapterLabel,
                              style: chapterStyle,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          onTap: () {
                            Navigator.of(context).pop();
                            widget.epubPlayerKey.currentState!.goToCfi(
                              subitem.cfi,
                            );
                            widget.hideAppBarAndBottomBar(false);
                          },
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
