import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/dao/theme.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/read_theme.dart';
import 'package:dasso_reader/page/book_player/epub_player.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';

import 'package:dasso_reader/widgets/reading_page/more_settings/more_settings.dart';
import 'package:dasso_reader/widgets/reading_page/style_widget.dart';
import 'package:dasso_reader/widgets/reading_page/widget_title.dart';
import 'package:flutter/material.dart';

class LightWidget extends StatefulWidget {
  const LightWidget({
    super.key,
    required this.themes,
    required this.epubPlayerKey,
    required this.setCurrentPage,
  });

  final List<ReadTheme> themes;
  final GlobalKey<EpubPlayerState> epubPlayerKey;
  final Function setCurrentPage;

  @override
  State<LightWidget> createState() => _LightWidgetState();
}

class _LightWidgetState extends State<LightWidget> {
  int? currentThemeId = Prefs().readTheme.id;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          // Add 24px top padding for consistent spacing
          const SizedBox(height: 24.0),
          widgetTitle(
            L10n.of(context).reading_page_theme,
            ReadingSettings.theme,
          ),
          themeSelector(),
          // Add bottom padding for consistent spacing
          const SizedBox(height: 24.0),
        ],
      ),
    );
  }

  SizedBox themeSelector() {
    const size = 50.0;
    const paddingSize = 5.0;
    EdgeInsetsGeometry padding = const EdgeInsets.all(paddingSize);

    return SizedBox(
      height: size + paddingSize * 2,
      child: ListView.builder(
        itemCount: widget.themes.length + 1,
        scrollDirection: Axis.horizontal,
        itemBuilder: (context, index) {
          if (index == widget.themes.length) {
            // add a new theme
            return Padding(
              padding: padding,
              child: Container(
                padding: padding,
                width: size,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(50),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline,
                    width: 1,
                  ),
                ),
                child: SemanticHelpers.button(
                  context: context,
                  label: 'Add new reading theme',
                  hint: 'Creates a new custom reading theme for editing',
                  onTap: () async {
                    int currId = await insertTheme(
                      ReadTheme(
                        backgroundColor: 'ff121212',
                        textColor: 'ffcccccc',
                        backgroundImagePath: '',
                      ),
                    );
                    widget.setCurrentPage(
                      ThemeChangeWidget(
                        readTheme: ReadTheme(
                          id: currId,
                          backgroundColor: 'ff121212',
                          textColor: 'ffcccccc',
                          backgroundImagePath: '',
                        ),
                        setCurrentPage: widget.setCurrentPage,
                      ),
                    );
                  },
                  child: InkWell(
                    onTap: () async {
                      int currId = await insertTheme(
                        ReadTheme(
                          backgroundColor: 'ff121212',
                          textColor: 'ffcccccc',
                          backgroundImagePath: '',
                        ),
                      );
                      widget.setCurrentPage(
                        ThemeChangeWidget(
                          readTheme: ReadTheme(
                            id: currId,
                            backgroundColor: 'ff121212',
                            textColor: 'ffcccccc',
                            backgroundImagePath: '',
                          ),
                          setCurrentPage: widget.setCurrentPage,
                        ),
                      );
                    },
                    child: Icon(
                      Icons.add,
                      size: size / 2,
                      color: Color(
                        int.parse(
                          '0x${'ffcccccc'}',
                        ),
                      ),
                    ), // Use consistent color like anx-reader
                  ),
                ),
              ),
            );
          }
          // theme list
          return Padding(
            padding: padding,
            child: Container(
              padding: padding,
              decoration: BoxDecoration(
                color: Color(
                  int.parse('0x${widget.themes[index].backgroundColor}'),
                ),
                borderRadius: BorderRadius.circular(50),
                border: Border.all(
                  color: index + 1 == currentThemeId
                      ? Theme.of(context).primaryColor
                      : Theme.of(context).colorScheme.outline,
                  width: index + 1 == currentThemeId ? 3 : 1,
                ),
              ),
              height: size,
              width: size,
              child: SemanticHelpers.button(
                context: context,
                label: 'Reading theme ${index + 1}',
                hint: index + 1 == currentThemeId
                    ? 'Currently selected theme. Long press to edit'
                    : 'Tap to apply this reading theme. Long press to edit',
                onTap: () {
                  Prefs().saveReadThemeToPrefs(widget.themes[index]);
                  widget.epubPlayerKey.currentState!
                      .changeTheme(widget.themes[index]);

                  // Update UI for all reading controls
                  setState(() {
                    currentThemeId = widget.themes[index].id;
                  });
                },
                child: InkWell(
                  onTap: () {
                    Prefs().saveReadThemeToPrefs(widget.themes[index]);
                    widget.epubPlayerKey.currentState!
                        .changeTheme(widget.themes[index]);

                    // Update UI for all reading controls
                    setState(() {
                      currentThemeId = widget.themes[index].id;
                    });
                  },
                  onLongPress: () {
                    setState(() {
                      widget.setCurrentPage(
                        ThemeChangeWidget(
                          readTheme: widget.themes[index],
                          setCurrentPage: widget.setCurrentPage,
                        ),
                      );
                    });
                  },
                  child: Center(
                    child: Text(
                      'A',
                      style: TextStyle(
                        color: Color(
                          int.parse('0x${widget.themes[index].textColor}'),
                        ),
                        fontSize: size / 3,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
