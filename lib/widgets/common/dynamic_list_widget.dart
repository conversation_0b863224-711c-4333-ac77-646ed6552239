import 'package:flutter/material.dart';
import 'package:dasso_reader/config/responsive_system.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/enums/dynamic_content_types.dart';
import 'package:dasso_reader/widgets/common/dynamic_text_widget.dart';

/// A comprehensive list widget that adapts to different content types and states
///
/// This widget automatically handles:
/// - Loading states and empty states
/// - Adaptive item sizing based on screen size
/// - Pull-to-refresh functionality
/// - Infinite scroll/pagination
/// - Different list content types
class DynamicListWidget<T> extends StatelessWidget {
  const DynamicListWidget({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.contentType = ListContentType.general,
    this.loadingState = LoadingState.initial,
    this.emptyStateType = EmptyStateType.general,
    this.scrollController,
    this.onRefresh,
    this.onLoadMore,
    this.hasMore = false,
    this.isLoadingMore = false,
    this.shrinkWrap = false,
    this.physics,
    this.padding,
    this.separatorBuilder,
    this.emptyWidget,
    this.loadingWidget,
    this.errorWidget,
    this.onRetry,
    this.semanticsLabel,
  });

  /// List of items to display
  final List<T> items;

  /// Builder function for list items
  final Widget Function(BuildContext context, T item, int index) itemBuilder;

  /// Type of list content
  final ListContentType contentType;

  /// Current loading state
  final LoadingState loadingState;

  /// Type of empty state to show
  final EmptyStateType emptyStateType;

  /// Scroll controller
  final ScrollController? scrollController;

  /// Pull-to-refresh callback
  final Future<void> Function()? onRefresh;

  /// Load more items callback
  final Future<void> Function()? onLoadMore;

  /// Whether there are more items to load
  final bool hasMore;

  /// Whether currently loading more items
  final bool isLoadingMore;

  /// Whether the list should shrink wrap
  final bool shrinkWrap;

  /// Scroll physics
  final ScrollPhysics? physics;

  /// List padding
  final EdgeInsetsGeometry? padding;

  /// Separator builder for divided lists
  final Widget Function(BuildContext context, int index)? separatorBuilder;

  /// Custom empty state widget
  final Widget? emptyWidget;

  /// Custom loading widget
  final Widget? loadingWidget;

  /// Custom error widget
  final Widget? errorWidget;

  /// Retry callback for error states
  final VoidCallback? onRetry;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  @override
  Widget build(BuildContext context) {
    Widget listWidget = _buildListContent(context);

    // Add pull-to-refresh if callback provided
    if (onRefresh != null) {
      listWidget = RefreshIndicator(
        onRefresh: onRefresh!,
        child: listWidget,
      );
    }

    // Add semantics if provided
    if (semanticsLabel != null) {
      listWidget = Semantics(
        label: semanticsLabel,
        child: listWidget,
      );
    }

    return listWidget;
  }

  Widget _buildListContent(BuildContext context) {
    switch (loadingState) {
      case LoadingState.loading:
        return _buildLoadingWidget(context);

      case LoadingState.error:
        return _buildErrorWidget(context);

      case LoadingState.empty:
        return _buildEmptyWidget(context);

      case LoadingState.success:
      case LoadingState.initial:
      case LoadingState.refreshing:
        if (items.isEmpty) {
          return _buildEmptyWidget(context);
        }
        return _buildList(context);
    }
  }

  Widget _buildList(BuildContext context) {
    final effectivePadding =
        padding ?? _getDefaultPaddingForContentType(context);
    final itemCount =
        items.length + (hasMore ? 1 : 0); // Add 1 for loading indicator

    Widget listWidget;

    if (separatorBuilder != null) {
      // Use ListView.separated for divided lists
      listWidget = ListView.separated(
        controller: scrollController,
        shrinkWrap: shrinkWrap,
        physics: physics,
        padding: effectivePadding,
        itemCount: items.length,
        itemBuilder: (context, index) => _buildListItem(context, index),
        separatorBuilder: separatorBuilder!,
      );
    } else {
      // Use regular ListView.builder
      listWidget = ListView.builder(
        controller: scrollController,
        shrinkWrap: shrinkWrap,
        physics: physics,
        padding: effectivePadding,
        itemCount: itemCount,
        itemBuilder: (context, index) {
          if (index < items.length) {
            return _buildListItem(context, index);
          } else {
            // Loading more indicator
            return _buildLoadMoreWidget(context);
          }
        },
      );
    }

    // Add scroll listener for infinite scroll
    if (onLoadMore != null && scrollController != null) {
      return NotificationListener<ScrollNotification>(
        onNotification: (scrollInfo) {
          if (scrollInfo is ScrollEndNotification &&
              scrollController!.position.extentAfter < 200 &&
              hasMore &&
              !isLoadingMore) {
            onLoadMore!();
          }
          return false;
        },
        child: listWidget,
      );
    }

    return listWidget;
  }

  Widget _buildListItem(BuildContext context, int index) {
    final item = items[index];
    final constraints =
        ResponsiveSystem.getAdaptiveListItemConstraints(context);

    return ConstrainedBox(
      constraints: constraints,
      child: itemBuilder(context, item, index),
    );
  }

  Widget _buildLoadingWidget(BuildContext context) {
    if (loadingWidget != null) return loadingWidget!;

    return const Center(
      child: Padding(
        padding: EdgeInsets.all(DesignSystem.spaceXL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            DesignSystem.verticalSpaceM,
            DynamicTextWidget(
              'Loading...',
              contentType: TextContentType.info,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context) {
    if (errorWidget != null) return errorWidget!;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(DesignSystem.spaceXL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Theme.of(context).colorScheme.error,
            ),
            DesignSystem.verticalSpaceM,
            const DynamicTextWidget(
              'Failed to load content',
              contentType: TextContentType.error,
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              DesignSystem.verticalSpaceM,
              ElevatedButton(
                onPressed: onRetry,
                child: const Text('Retry'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyWidget(BuildContext context) {
    if (emptyWidget != null) return emptyWidget!;

    final (icon, message) = _getEmptyStateIconAndMessage();

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(DesignSystem.spaceXL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            DesignSystem.verticalSpaceL,
            DynamicTextWidget(
              message,
              contentType: TextContentType.info,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadMoreWidget(BuildContext context) {
    if (!hasMore) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.all(DesignSystem.spaceM),
      child: Center(
        child: isLoadingMore
            ? const CircularProgressIndicator()
            : TextButton(
                onPressed: onLoadMore,
                child: const Text('Load more'),
              ),
      ),
    );
  }

  EdgeInsetsGeometry _getDefaultPaddingForContentType(BuildContext context) {
    switch (contentType) {
      case ListContentType.books:
        return ResponsiveSystem.getAdaptiveContentPadding(context);
      case ListContentType.notes:
      case ListContentType.searchResults:
      case ListContentType.dictionary:
        return const EdgeInsets.symmetric(
          horizontal: DesignSystem.spaceM,
          vertical: DesignSystem.spaceS,
        );
      case ListContentType.settings:
      case ListContentType.navigation:
        return EdgeInsets.zero;
      case ListContentType.hskCharacters:
        return const EdgeInsets.all(DesignSystem.spaceS);
      case ListContentType.history:
      case ListContentType.bookmarks:
        return const EdgeInsets.all(DesignSystem.spaceM);
      case ListContentType.general:
        return const EdgeInsets.all(DesignSystem.spaceM);
    }
  }

  (IconData, String) _getEmptyStateIconAndMessage() {
    switch (emptyStateType) {
      case EmptyStateType.noBooks:
        return (Icons.library_books_outlined, 'No books in your library');
      case EmptyStateType.noSearchResults:
        return (Icons.search_off, 'No search results found');
      case EmptyStateType.noNotes:
        return (Icons.note_outlined, 'No notes available');
      case EmptyStateType.noBookmarks:
        return (Icons.bookmark_border, 'No bookmarks saved');
      case EmptyStateType.noHistory:
        return (Icons.history, 'No reading history');
      case EmptyStateType.networkError:
        return (Icons.wifi_off, 'Network connection error');
      case EmptyStateType.permissionDenied:
        return (Icons.lock_outline, 'Permission denied');
      case EmptyStateType.notFound:
        return (Icons.search_off, 'Content not found');
      case EmptyStateType.general:
        return (Icons.inbox_outlined, 'No content available');
    }
  }
}

/// A specialized grid widget for dynamic content
class DynamicGridWidget<T> extends StatelessWidget {
  const DynamicGridWidget({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.contentType = GridContentType.general,
    this.loadingState = LoadingState.initial,
    this.emptyStateType = EmptyStateType.general,
    this.crossAxisCount,
    this.childAspectRatio,
    this.spacing,
    this.runSpacing,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.onRefresh,
    this.emptyWidget,
    this.loadingWidget,
    this.errorWidget,
    this.onRetry,
  });

  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final GridContentType contentType;
  final LoadingState loadingState;
  final EmptyStateType emptyStateType;
  final int? crossAxisCount;
  final double? childAspectRatio;
  final double? spacing;
  final double? runSpacing;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final Future<void> Function()? onRefresh;
  final Widget? emptyWidget;
  final Widget? loadingWidget;
  final Widget? errorWidget;
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context) {
    Widget gridWidget = _buildGridContent(context);

    if (onRefresh != null) {
      gridWidget = RefreshIndicator(
        onRefresh: onRefresh!,
        child: gridWidget,
      );
    }

    return gridWidget;
  }

  Widget _buildGridContent(BuildContext context) {
    switch (loadingState) {
      case LoadingState.loading:
        return _buildLoadingWidget(context);
      case LoadingState.error:
        return _buildErrorWidget(context);
      case LoadingState.empty:
        return _buildEmptyWidget(context);
      case LoadingState.success:
      case LoadingState.initial:
      case LoadingState.refreshing:
        if (items.isEmpty) {
          return _buildEmptyWidget(context);
        }
        return _buildGrid(context);
    }
  }

  Widget _buildGrid(BuildContext context) {
    final effectiveCrossAxisCount =
        crossAxisCount ?? ResponsiveSystem.getAdaptiveColumnCount(context);

    final effectiveChildAspectRatio =
        childAspectRatio ?? _getDefaultAspectRatioForContentType();

    final effectiveSpacing =
        spacing ?? ResponsiveSystem.getAdaptiveGridSpacing(context);

    final effectivePadding =
        padding ?? ResponsiveSystem.getAdaptiveContentPadding(context);

    return GridView.builder(
      shrinkWrap: shrinkWrap,
      physics: physics,
      padding: effectivePadding,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: effectiveCrossAxisCount,
        childAspectRatio: effectiveChildAspectRatio,
        crossAxisSpacing: effectiveSpacing,
        mainAxisSpacing: runSpacing ?? effectiveSpacing,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        final constraints =
            ResponsiveSystem.getAdaptiveGridItemConstraints(context);

        return ConstrainedBox(
          constraints: constraints,
          child: itemBuilder(context, item, index),
        );
      },
    );
  }

  Widget _buildLoadingWidget(BuildContext context) {
    if (loadingWidget != null) return loadingWidget!;
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildErrorWidget(BuildContext context) {
    if (errorWidget != null) return errorWidget!;
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.error_outline, size: 48),
          const SizedBox(height: 16),
          const Text('Failed to load content'),
          if (onRetry != null)
            ElevatedButton(onPressed: onRetry, child: const Text('Retry')),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget(BuildContext context) {
    if (emptyWidget != null) return emptyWidget!;
    return const Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.grid_view, size: 64),
          SizedBox(height: 16),
          Text('No items to display'),
        ],
      ),
    );
  }

  double _getDefaultAspectRatioForContentType() {
    switch (contentType) {
      case GridContentType.books:
        return 0.65; // Book cover aspect ratio
      case GridContentType.themes:
      case GridContentType.colors:
        return 1.0; // Square
      case GridContentType.icons:
        return 1.0; // Square
      case GridContentType.gallery:
        return 1.2; // Slightly wide
      case GridContentType.settings:
        return 1.5; // Wide
      case GridContentType.general:
        return 1.0; // Square
    }
  }
}
