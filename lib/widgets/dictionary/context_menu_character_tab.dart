import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/service/dictionary/dictionary_service.dart';
import 'package:dasso_reader/models/dictionary/dictionary_entry.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A specialized character component for the context menu
/// Displays individual Chinese characters with their cc-cedict definitions
class ContextMenuCharacterTab extends StatefulWidget {
  /// The selected text to extract characters from
  final String selectedText;

  const ContextMenuCharacterTab({
    super.key,
    required this.selectedText,
  });

  @override
  State<ContextMenuCharacterTab> createState() =>
      _ContextMenuCharacterTabState();
}

class _ContextMenuCharacterTabState extends State<ContextMenuCharacterTab> {
  bool _isLoading = true;
  List<Map<String, dynamic>> _characterOptions = [];
  final DictionaryService _dictionaryService = DictionaryService();

  @override
  void initState() {
    super.initState();
    _loadCharacterData();
  }

  /// Load character definitions from cc-cedict with polyphonic character support
  Future<void> _loadCharacterData() async {
    try {
      await _dictionaryService.initialize();

      final characters = <Map<String, dynamic>>[];
      final processedChars = <String>{};

      // Extract unique Chinese characters from the selected text
      for (int i = 0; i < widget.selectedText.length; i++) {
        final char = widget.selectedText[i];

        // Check if it's a Chinese character and not already processed
        if (_isChinese(char) && !processedChars.contains(char)) {
          processedChars.add(char);

          // Look up ALL character definitions for polyphonic character support
          final entries = await _dictionaryService.lookupChineseAll(char);

          if (entries.isNotEmpty) {
            // Add each pronunciation variant as a separate entry
            for (int entryIndex = 0;
                entryIndex < entries.length;
                entryIndex++) {
              final entry = entries[entryIndex];
              characters.add({
                'character': char,
                'entry': entry,
                'isSelected': false,
                'variantIndex': entryIndex, // Track which variant this is
                'totalVariants':
                    entries.length, // Track total variants for this character
              });
            }
          } else {
            // Fallback: try single lookup if lookupChineseAll returns empty
            final entry = await _dictionaryService.lookupChinese(char);
            characters.add({
              'character': char,
              'entry': entry,
              'isSelected': false,
              'variantIndex': 0,
              'totalVariants': 1,
            });
          }
        }
      }

      _characterOptions = characters;

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Handle character selection with polyphonic variant support
  void _onCharacterSelected(String character, String pinyin) {
    // Copy the selected character with pinyin to clipboard for better context
    final clipboardText = '$character ($pinyin)';
    Clipboard.setData(ClipboardData(text: clipboardText));

    // Show feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text('${L10n.of(context).notes_page_copied}: "$clipboardText"'),
        duration: const Duration(seconds: 2),
      ),
    );

    // Update selection state - select the specific variant
    setState(() {
      for (var option in _characterOptions) {
        final entry = option['entry'] as DictionaryEntry?;
        option['isSelected'] =
            option['character'] == character && entry?.pinyin == pinyin;
      }
    });
  }

  /// Check if text is Chinese
  bool _isChinese(String text) {
    return RegExp(r'[\u4e00-\u9fa5]').hasMatch(text);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final readingTextColor =
        Color(int.parse('0x${Prefs().readTheme.textColor}'));

    if (_isLoading) {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 18,
                height: 18,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                ),
              ),
              const SizedBox(height: 6),
              Text(
                'Loading characters...',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: readingTextColor.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_characterOptions.isEmpty) {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
        child: Center(
          child: Text(
            'No Chinese characters found',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: readingTextColor.withValues(alpha: 0.7),
            ),
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 2.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: _characterOptions
            .map(
              (option) => Padding(
                padding: const EdgeInsets.only(bottom: 2),
                child: _buildCompactCharacterCard(option),
              ),
            )
            .toList(),
      ),
    );
  }

  /// Build a compact character card following Material Design 3 principles
  Widget _buildCompactCharacterCard(Map<String, dynamic> option) {
    final character = option['character'] as String;
    final entry = option['entry'] as DictionaryEntry?;
    final isSelected = option['isSelected'] as bool;
    final variantIndex = option['variantIndex'] as int;
    final totalVariants = option['totalVariants'] as int;
    final theme = Theme.of(context);

    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      color: isSelected
          ? theme.colorScheme.primaryContainer.withValues(alpha: 0.3)
          : theme.colorScheme.surfaceContainerLow,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: isSelected
              ? theme.colorScheme.primary
              : theme.colorScheme.outline.withValues(alpha: 0.2),
          width: isSelected ? 1.5 : 0.5,
        ),
      ),
      child: InkWell(
        onTap: () => _onCharacterSelected(character, entry?.pinyin ?? ''),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with character, pinyin, and HSK
              Row(
                children: [
                  // Character with variant indicator
                  Stack(
                    clipBehavior: Clip.none,
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color:
                              theme.colorScheme.primary.withValues(alpha: 0.15),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Center(
                          child: Text(
                            character,
                            style: theme.textTheme.titleSmall?.copyWith(
                              color: theme.colorScheme.primary,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),
                      // Polyphonic variant indicator
                      if (totalVariants > 1)
                        Positioned(
                          top: -4,
                          right: -4,
                          child: Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              color: theme.colorScheme.tertiary,
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(
                                color: theme.colorScheme.surface,
                                width: 1,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                '${variantIndex + 1}',
                                style: theme.textTheme.labelSmall?.copyWith(
                                  color: theme.colorScheme.onTertiary,
                                  fontSize: 8,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                  // Pinyin and HSK in same row
                  Expanded(
                    child: Row(
                      children: [
                        if (entry != null && entry.pinyin.isNotEmpty) ...[
                          Text(
                            entry.formattedPinyin(),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.primary,
                              fontStyle: FontStyle.italic,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                        const Spacer(),
                        // HSK badge
                        if (entry != null &&
                            entry.hskLevel != null &&
                            entry.hskLevel! > 0)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 4,
                              vertical: 1,
                            ),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.secondaryContainer,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              'HSK${entry.hskLevel!}',
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: theme.colorScheme.onSecondaryContainer,
                                fontSize: 9,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        // Selection indicator
                        if (isSelected) ...[
                          const SizedBox(width: 4),
                          Icon(
                            Icons.check_circle,
                            color: theme.colorScheme.primary,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),

              // Definitions in a compact container
              if (entry != null && entry.definitions.isNotEmpty) ...[
                const SizedBox(height: 4),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHighest
                        .withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: entry.definitions
                        .take(3)
                        .map(
                          (definition) => Padding(
                            padding: const EdgeInsets.only(bottom: 1),
                            child: Text(
                              '• $definition',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurface
                                    .withValues(alpha: 0.8),
                                fontSize: 10,
                                height: 1.2,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        )
                        .toList(),
                  ),
                ),
              ] else ...[
                const SizedBox(height: 4),
                Text(
                  'No definition available',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                    fontStyle: FontStyle.italic,
                    fontSize: 10,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
