import 'dart:io';

import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:flutter/material.dart';

/// Optimized book cover widget that avoids expensive file system operations
/// in the build method by using FutureBuilder for async file checking
Widget bookCover(
  BuildContext context,
  Book book, {
  double? height,
  double? width,
  double? radius,
}) {
  radius ??= DesignSystem.radiusS;

  return Container(
    height: height,
    width: width,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(DesignSystem.radiusS - 1),
      border: Border.all(
        width: 0.5, // Thin border for book cover outline
        color: Theme.of(context).colorScheme.outline,
      ),
    ),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(radius),
      child: _OptimizedBookCoverContent(book: book),
    ),
  );
}

/// Optimized book cover content that handles file existence checking asynchronously
class _OptimizedBookCoverContent extends StatelessWidget {
  const _OptimizedBookCoverContent({required this.book});

  final Book book;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: _checkFileExists(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          // Show placeholder while checking file existence
          return _BookCoverPlaceholder(book: book);
        }

        final fileExists = snapshot.data ?? false;
        if (fileExists) {
          return Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: FileImage(File(book.coverFullPath)),
                fit: BoxFit.cover,
              ),
            ),
          );
        } else {
          return _BookCoverPlaceholder(book: book);
        }
      },
    );
  }

  /// Async file existence check to avoid blocking the UI thread
  Future<bool> _checkFileExists() async {
    try {
      final file = File(book.coverFullPath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }
}

/// Optimized book cover placeholder that only rebuilds when book changes
///
/// This widget is extracted to minimize rebuilds and uses const constructor
/// for better performance.
class _BookCoverPlaceholder extends StatelessWidget {
  const _BookCoverPlaceholder({
    required this.book,
  });

  final Book book;

  @override
  Widget build(BuildContext context) {
    // Use theme-aware colors for book cover placeholder
    final colorScheme = Theme.of(context).colorScheme;
    final backgroundColor = colorScheme.surfaceContainerHighest;

    return Container(
      color: backgroundColor,
      child: Center(
        child: Icon(
          Icons.book,
          size: DesignSystem.widgetIconSizeLarge,
          color: colorScheme.onSurface,
        ),
      ),
    );
  }
}
