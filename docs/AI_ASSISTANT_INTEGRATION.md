# 🤖 AI Assistant Integration Guide - DassoShu Reader Design System

## 🎯 **MAND<PERSON>ORY AI ASSISTANT INSTRUCTIONS**

**Copy this section to every AI conversation for automatic design system compliance:**

---

### **🏗️ DASSOSHU READER DESIGN SYSTEM REQUIREMENTS**

**CRITICAL: Every UI component MUST follow these non-negotiable patterns:**

#### **1. FORBIDDEN PATTERNS (Will cause lint errors)**
```dart
// ❌ NEVER USE - These will fail CI/CD
padding: EdgeInsets.all(16.0)           // Use DesignSystem.spaceM
margin: EdgeInsets.only(top: 8.0)       // Use DesignSystem.spaceS
fontSize: 14.0                          // Use DesignSystem.fontSizeM
color: Colors.black                     // Use Theme.of(context).colorScheme
fontWeight: FontWeight.w600             // Use getAdjustedFontWeight()
```

#### **2. MANDATORY PATTERNS (Required for all components)**
```dart
// ✅ ALWAYS USE - These are required
padding: EdgeInsets.all(DesignSystem.spaceM)
fontSize: DesignSystem.fontSizeM
fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600)
iconSize: DesignSystem.getAdjustedIconSize(24.0)
color: Theme.of(context).colorScheme.onSurface
```

#### **3. HIGH-PRIORITY COMPONENTS (Require pixel-perfect adjustments)**
- Navigation bars, tab bars, context menus
- Reading interface controls
- Settings pages, list tiles
- Buttons, icons in primary UI areas

**For these components, ALWAYS use manufacturer adjustments:**
```dart
// ✅ MANDATORY for high-priority components
fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600)
iconSize: DesignSystem.getAdjustedIconSize(DesignSystem.widgetIconSizeMedium)
padding: DesignSystem.getAdaptivePaddingWithManufacturerAdjustment(context, basePadding)
```

---

## 📋 **AI ASSISTANT CHECKLIST**

Before generating any Flutter code, verify:

### **✅ SPACING COMPLIANCE**
- [ ] No hardcoded padding/margin values
- [ ] All spacing uses DesignSystem.spaceXS/S/M/L/XL
- [ ] Manufacturer adjustments applied to high-priority spacing

### **✅ TYPOGRAPHY COMPLIANCE**
- [ ] No hardcoded font sizes
- [ ] All font sizes use DesignSystem.fontSizeXS/S/M/L/XL
- [ ] High-visibility text uses getAdjustedFontWeight()

### **✅ COLOR COMPLIANCE**
- [ ] No hardcoded colors (Colors.black, Colors.white, etc.)
- [ ] All colors use Theme.of(context).colorScheme
- [ ] Proper contrast ratios maintained

### **✅ ICON COMPLIANCE**
- [ ] No hardcoded icon sizes
- [ ] Navigation/button icons use getAdjustedIconSize()
- [ ] Icon colors use theme colors

### **✅ ACCESSIBILITY COMPLIANCE**
- [ ] Touch targets minimum 44dp (DesignSystem.widgetMinTouchTarget)
- [ ] Proper semantic labels for screen readers
- [ ] WCAG AAA color contrast ratios

---

## 🎨 **COMPONENT-SPECIFIC REQUIREMENTS**

### **Navigation Components**
```dart
// ✅ MANDATORY: Full pixel-perfect implementation
TabBar(
  labelStyle: TextStyle(
    fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
    fontSize: DesignSystem.fontSizeM,
  ),
  labelPadding: DesignSystem.getAdaptivePaddingWithManufacturerAdjustment(
    context,
    EdgeInsets.symmetric(horizontal: DesignSystem.spaceS),
  ),
)
```

### **Text Components**
```dart
// ✅ MANDATORY: For headers, labels, important text
Text(
  'Important Text',
  style: TextStyle(
    fontSize: DesignSystem.fontSizeM,
    fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
    color: Theme.of(context).colorScheme.onSurface,
  ),
)
```

### **Container Components**
```dart
// ✅ MANDATORY: Standard container pattern
Container(
  padding: EdgeInsets.all(DesignSystem.spaceM),
  margin: EdgeInsets.symmetric(
    horizontal: DesignSystem.spaceS,
    vertical: DesignSystem.spaceXS,
  ),
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(DesignSystem.radiusM),
    color: Theme.of(context).colorScheme.surface,
  ),
  child: content,
)
```

### **Button Components**
```dart
// ✅ MANDATORY: Proper touch targets and styling
ElevatedButton(
  style: ElevatedButton.styleFrom(
    minimumSize: Size.fromHeight(DesignSystem.widgetMinTouchTarget),
    padding: EdgeInsets.symmetric(
      horizontal: DesignSystem.spaceL,
      vertical: DesignSystem.spaceS,
    ),
  ),
  child: Text(
    'Button',
    style: TextStyle(
      fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
    ),
  ),
  onPressed: onPressed,
)
```

---

## 🔧 **QUICK REFERENCE FOR AI ASSISTANTS**

### **Essential Constants**
```dart
// Spacing (ALWAYS use these)
DesignSystem.spaceXS    // 4.0
DesignSystem.spaceS     // 8.0
DesignSystem.spaceM     // 16.0
DesignSystem.spaceL     // 24.0
DesignSystem.spaceXL    // 32.0

// Font Sizes (ALWAYS use these)
DesignSystem.fontSizeXS // 10.0
DesignSystem.fontSizeS  // 12.0
DesignSystem.fontSizeM  // 14.0
DesignSystem.fontSizeL  // 16.0
DesignSystem.fontSizeXL // 18.0

// Border Radius (ALWAYS use these)
DesignSystem.radiusS    // 4.0
DesignSystem.radiusM    // 8.0
DesignSystem.radiusL    // 16.0
```

### **Essential Methods**
```dart
// Manufacturer Adjustments (Use for high-priority components)
DesignSystem.getAdjustedFontWeight(FontWeight.w600)
DesignSystem.getAdjustedIconSize(24.0)
DesignSystem.getAdaptiveSpacingWithManufacturerAdjustment(context, spacing)
DesignSystem.getAdaptivePaddingWithManufacturerAdjustment(context, padding)

// Responsive Design (Use for all components)
DesignSystem.getAdaptivePadding(context)
DesignSystem.isSmallPhone(context)
DesignSystem.isTablet(context)
```

### **Theme Colors (ALWAYS use these)**
```dart
// Primary Colors
Theme.of(context).colorScheme.primary
Theme.of(context).colorScheme.onPrimary
Theme.of(context).colorScheme.surface
Theme.of(context).colorScheme.onSurface

// Never use: Colors.black, Colors.white, Colors.blue, etc.
```

---

## 🚨 **CRITICAL REMINDERS FOR AI ASSISTANTS**

1. **NEVER generate hardcoded values** - Always use DesignSystem constants
2. **ALWAYS apply manufacturer adjustments** to navigation, text, and icons in high-priority areas
3. **ALWAYS use theme colors** - Never hardcode color values
4. **ALWAYS ensure 44dp touch targets** for interactive elements
5. **ALWAYS use const constructors** where possible for performance

### **When in doubt, ask:**
- "Should this component use manufacturer adjustments?"
- "Is this spacing using DesignSystem constants?"
- "Are colors coming from the theme?"
- "Does this meet accessibility requirements?"

---

## 📚 **REFERENCE DOCUMENTATION**

For complete details, refer to:
- `docs/DESIGN_SYSTEM_ENFORCEMENT.md` - Complete compliance rules
- `docs/CODE_TEMPLATES.md` - Copy-paste templates
- `lib/config/design_system.dart` - All available constants and methods

**Remember: These are not suggestions - they are mandatory requirements for maintaining professional, cross-manufacturer consistent user experience in DassoShu Reader.**
